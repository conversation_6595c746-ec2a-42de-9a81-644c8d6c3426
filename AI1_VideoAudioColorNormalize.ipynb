#%%
# pydub for rebalancing audio
# deepfilter thing for background noise removal
# torch sampling audio back to 48khz so can use rest https://docs.pytorch.org/audio/main/tutorials/audio_resampling_tutorial.html

from df.enhance import enhance, init_df, load_audio, save_audio
from pydub import AudioSegment
import torchaudio
import cv2
import ffmpeg as ff
from skimage.exposure import match_histograms
import matplotlib.pyplot as plt
import numpy as np
import librosa as lib
import soundfile as sf
from scipy.signal import butter, lfilter
from ffmpeg import Error as ffErr
import os

def extract_audio_from_video(video_path, vid_name, audio_path):
    try:
        (
            ff
            .input(video_path + vid_name)
            .output(audio_path, acodec='pcm_s16le', ac=2, ar='48000')
            .overwrite_output()
            .run(capture_stdout=True, capture_stderr=True)
        )
    except ff.Error as e:
        print("FFmpeg stdout:", e.stdout.decode('utf8'))
        print("FFmpeg stderr:", e.stderr.decode('utf8'))
        raise e

def swap_audio(video_path, new_audio_path, output_path):
    video = ff.input(video_path)
    audio = ff.input(new_audio_path)
    try:
        (
            ff
            .concat(video, audio, v=1, a=1)
            .output(output_path)
            .run(overwrite_output=True)
        )
    except ffErr as e:
        print(e.stderr.decode('utf8'))
        print(e.stdout.decode('utf8'))
        raise e

def denoise_and_normalise(audio_file, music_proportion):
    if not os.path.exists(audio_file):
        print(f"Audio file '{audio_file}' not found.")
        return ""
    
    torchaudio.set_audio_backend('soundfile')

    # https://paperswithcode.com/paper/deepfilternet-perceptually-motivated-real#code

    # Load default model
    model, df_state, _ = init_df()
    audio_path = audio_file

    audio, _ = load_audio(audio_path, sr=df_state.sr())
    enhanced = enhance(model, df_state, audio)
    save_audio(audio_file + "_enhanced_deepfilter.wav", enhanced, df_state.sr())

    # Audio rebalancing - basically just change volume back -> music to voice ratio handled with existing pipeline
    if (music_proportion == 1):
        target_dBFS = 0
    else :
        target_dBFS = -20
        if (music_proportion > 0.5):
            ratio =  ((1 - music_proportion)/ (music_proportion))
            target_dBFS = retarget_audio(ratio) + 10

    audio = AudioSegment.from_file(audio_file + "_enhanced_deepfilter.wav")
    change = target_dBFS - audio.dBFS
    normalized = audio.apply_gain(change)
    normalized.export(audio_file + "_normalized_deepfilter.wav", format="wav")

    enhanced_n= audio_file + "_enhanced_deepfilter.wav"
    normalized_n = audio_file + "_normalized_deepfilter.wav"
    return enhanced_n, normalized_n

def recolor_video(video_path, video_name, reference_img):
    # Histogram based color matching
    if not os.path.exists(video_path + video_name):
        print(f"Video file '{video_path + video_name}' not found.")
        return ""

    vidcap = cv2.VideoCapture(video_path + video_name)
    success,image = vidcap.read()
    new_vid = []
    if len(reference_img) != 0 and os.path.exists(reference_img):
        histogram_match = cv2.imread(reference_img)
    else:
        if (not os.path.exists(reference_img)): print("Recolor reference image does not exist.")
        return video_path + video_name
    nb_frames = 0

    # Recolor frames 1 by 1
    while success:      
        # processing
        nb_frames += 1
        new_img = image
        if len(reference_img) != 0:
            new_img = match_histograms(image, histogram_match, channel_axis=-1)
        new_vid.append(new_img)
        success,image = vidcap.read()


    height,width,layers=new_vid[1].shape
    fourcc = cv2.VideoWriter_fourcc(*'mp4v')
    new_color_vid = cv2.VideoWriter('./tmp/' + video_name + '_stiched_back_vid.mp4', fourcc, 30, (width,height))
    for j in range(0, nb_frames):
        new_color_vid.write(new_vid[j])

    cv2.destroyAllWindows()
    new_color_vid.release()
    return './tmp/' + video_name + '_stiched_back_vid.mp4'

def add_music(video_file, music_file, music_proportion, output_file):
    temp_file_name = './tmp/shortened_' + music_file

    if not os.path.exists(video_file):
        print(f"Video file '{video_file}' not found.")
        return ""
    
    
    if not os.path.exists(music_file):
        print(f"Music file '{music_file}' not found.")
        return ""
    
    if (music_proportion == 0):
        (
            ff.input(video_file)
            .output(output_file, vcodec='copy', acodec='copy')
            .run(overwrite_output=True)
        )
        return

    elif (music_proportion == 1):
        target_dBFS = -20

    else:
        ratio =  ((music_proportion) / (1 - music_proportion))
        target_dBFS = retarget_audio(ratio)
        if (music_proportion > 0.5):
            target_dBFS = -20

    probe = ff.probe(video_file)
    duration = float(probe['format']['duration'])

    source_file_for_dBFS = music_file
    if (music_proportion <= 0.5):
        highpass(music_file, temp_file_name)
        source_file_for_dBFS = temp_file_name

    # Change volume and length of music
    audio = AudioSegment.from_wav(source_file_for_dBFS)
    audio = audio[0:duration*1000]
    
    change = target_dBFS - audio.dBFS
    normalized = audio.apply_gain(change)
    normalized.export(temp_file_name, format="wav")

    # Add music
    input_video = ff.input(video_file)
    added_audio = ff.input(temp_file_name)

    merged_audio = ff.filter([input_video.audio, added_audio], 'amix')

    (
        ff
        .concat(input_video, merged_audio, v=1, a=1)
        .output(output_file)
        .run(overwrite_output=True)
    )
    return 

def run_processing(original_vid_dir, original_vid, music_file, music_proportion, output_file, ref_color_file=""):

    if (not os.path.exists(original_vid_dir + original_vid)):
        print("Input video file not found.")
        return
    elif  ((music_proportion > 0 and not os.path.exists(music_file))):
        print("Input music file not found.")
        return

    audio_file = "tmp/02_audio.wav"
    swap_color_audio = "tmp/01_swapped_audio_and_color.mp4"
    
    extract_audio_from_video(original_vid_dir, original_vid, audio_file)

    _, normalized = denoise_and_normalise(audio_file, music_proportion)

    recolored = recolor_video(original_vid_dir, original_vid, ref_color_file)

    if (music_proportion != 1):
        swap_audio(recolored, normalized, swap_color_audio)
    else:
        swap_audio(recolored, music_file, swap_color_audio)

    
    add_music(swap_color_audio, music_file, music_proportion, output_file)

def retarget_audio(ratio):
    # current (ex 1/3 as loud) minus 1 (2/3 of the loudness to be removed) divided by two (how many halfs is 2/3) times 10 for the requisite noise 
    # Div by 2 and *10 as *20
    # Lets start at -32 cause music way too loud - this makes it about as loud as the rest of the clip
    return  -32 - (20 * (1 - ratio) * (1 - ratio))


def highpass(audio_file, temp_file, cutoff=200, order=5):
    if not os.path.exists(audio_file):
        print(f"Audio file '{audio_file}' not found.")
        return ""

    y, sr = lib.load(audio_file, sr=None)
    nyq = 0.5 * sr
    norm_cutoff = cutoff / nyq
    b, a = butter(order, norm_cutoff, btype='high', analog=False)
    filtered = lfilter(b, a, y)
    sf.write(temp_file, filtered, sr)


def run_test(music_proportion):
    # Run test pipeline
    original_vid_dir = "./testvids/"
    original_vid = "up.mp4"
    audio_file = "./tmp/02_audio.wav"
    ref_color_file = "./02-histogram.jpg"
    swap_color_audio = "./tmp/01_swapped_audio_and_color.mp4"
    music_file = "hyper_camelot.wav"
    output_file =  "00_" + str((1-music_proportion) * 100) + "-" + str(music_proportion*100) + ".mp4"
    
    extract_audio_from_video(original_vid_dir, original_vid, audio_file)

    _, normalized = denoise_and_normalise(audio_file, music_proportion) # cleanup and re-volume

    recolored = recolor_video(original_vid_dir, original_vid, ref_color_file)

    if (music_proportion != 1):
        swap_audio(recolored, normalized, swap_color_audio)
    else:
        swap_audio(recolored, music_file, swap_color_audio)

    
    add_music(swap_color_audio, music_file, music_proportion, output_file)

if __name__ == "__main__":
    # # Presets
    # # Toggleable highpass filter
    # # Normalised vs just enhanced clean audio

    original_vid_dir = "./testvids/"
    original_vid = "interview.mp4"
    ref_color_file = "02-histogram.jpg"
    music_file = "hyper_camelot.wav"
    output_file =  "00_final_out_newfct.mp4"

    # run_test(0)
    # run_test(0.1)
    # run_test(0.25)
    # run_test(0.33)
    # run_test(0.5)
    # run_test(0.66)
    # run_test(0.75)
    # run_test(0.9)
    # run_test(1)

    run_processing(original_vid_dir, original_vid, music_file, 0.33, output_file, ref_color_file) # TODO negative space programming -> validate args in all fcts
#%%
"""
Improved Video Processing Pipeline with Audio Enhancement and Color Correction
Claude my goat
"""

import os
import tempfile
import logging
from pathlib import Path
from dataclasses import dataclass
from typing import Optional, Tuple
import warnings

# Third-party imports
import cv2
import numpy as np
import ffmpeg as ff
from ffmpeg import Error as FFmpegError
import torchaudio
import librosa
import soundfile as sf
from scipy.signal import butter, lfilter
from pydub import AudioSegment
from skimage.exposure import match_histograms

# DeepFilter imports
try:
    from df.enhance import enhance, init_df, load_audio, save_audio
    DEEPFILTER_AVAILABLE = True
except ImportError:
    DEEPFILTER_AVAILABLE = False
    warnings.warn("DeepFilter not available. Audio denoising will be skipped.")

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


@dataclass
class ProcessingConfig:
    """Configuration for video processing pipeline."""
    audio_sample_rate: int = 48000
    video_codec: str = 'mp4v'
    audio_codec: str = 'pcm_s16le'
    audio_channels: int = 2
    highpass_cutoff: int = 200
    highpass_order: int = 5
    base_music_volume: int = -32
    target_voice_volume: int = -20
    default_fps: float = 30.0
    temp_dir: Optional[str] = None


class VideoProcessingError(Exception):
    """Custom exception for video processing errors."""
    pass


class VideoProcessor:
    """Video processing pipeline with audio enhancement and color correction."""
    
    def __init__(self, config: Optional[ProcessingConfig] = None):
        self.config = config or ProcessingConfig()
        self.temp_dir = Path(self.config.temp_dir or tempfile.gettempdir()) / "video_processing"
        self.temp_dir.mkdir(exist_ok=True, parents=True)
        
        # Initialize DeepFilter model if available
        self.df_model = None
        self.df_state = None
        if DEEPFILTER_AVAILABLE:
            try:
                self.df_model, self.df_state, _ = init_df()
                logger.info("DeepFilter model initialized successfully")
            except Exception as e:
                logger.warning(f"Failed to initialize DeepFilter: {e}")
                self.df_model = None
                self.df_state = None
        
        # Set audio backend
        try:
            torchaudio.set_audio_backend('soundfile')
        except Exception:
            pass  # Backend setting is deprecated in newer versions
    
    def validate_file(self, filepath: Path, file_type: str = "File") -> None:
        """Validate that a file exists and is readable."""
        if not filepath.exists():
            raise FileNotFoundError(f"{file_type} '{filepath}' not found")
        if not filepath.is_file():
            raise ValueError(f"{file_type} '{filepath}' is not a valid file")
    
    def get_video_properties(self, video_path: Path) -> dict:
        """Extract video properties using ffprobe."""
        try:
            probe = ff.probe(str(video_path))
            video_stream = next((s for s in probe['streams'] if s['codec_type'] == 'video'), None)
            audio_stream = next((s for s in probe['streams'] if s['codec_type'] == 'audio'), None)
            
            if not video_stream:
                raise VideoProcessingError("No video stream found")
            
            # Parse frame rate
            fps = self.config.default_fps
            if 'r_frame_rate' in video_stream:
                try:
                    fps_str = video_stream['r_frame_rate']
                    if '/' in fps_str:
                        num, den = map(int, fps_str.split('/'))
                        fps = num / den if den != 0 else self.config.default_fps
                except (ValueError, ZeroDivisionError):
                    logger.warning(f"Could not parse frame rate, using default: {fps}")
            
            return {
                'fps': fps,
                'duration': float(probe['format']['duration']),
                'width': int(video_stream.get('width', 0)),
                'height': int(video_stream.get('height', 0)),
                'has_audio': audio_stream is not None
            }
        except Exception as e:
            raise VideoProcessingError(f"Failed to get video properties: {e}")
    
    def extract_audio_from_video(self, video_path: Path, output_path: Path) -> None:
        """Extract audio from video file."""
        try:
            self.validate_file(video_path, "Video file")
            
            logger.info(f"Extracting audio from {video_path}")
            (
                ff.input(str(video_path))
                .output(
                    str(output_path),
                    acodec=self.config.audio_codec,
                    ac=self.config.audio_channels,
                    ar=str(self.config.audio_sample_rate)
                )
                .overwrite_output()
                .run(capture_stdout=True, capture_stderr=True, quiet=True)
            )
            logger.info(f"Audio extracted to {output_path}")
            
        except ff.Error as e:
            error_msg = e.stderr.decode('utf-8') if e.stderr else "Unknown FFmpeg error"
            raise VideoProcessingError(f"Failed to extract audio: {error_msg}")
    
    def denoise_and_normalize_audio(self, audio_path: Path, music_proportion: float) -> Tuple[Path, Path]:
        """Denoise and normalize audio using DeepFilter and volume adjustment."""
        self.validate_file(audio_path, "Audio file")
        
        enhanced_path = self.temp_dir / f"{audio_path.stem}_enhanced.wav"
        normalized_path = self.temp_dir / f"{audio_path.stem}_normalized.wav"
        
        # DeepFilter denoising if available
        if self.df_model and self.df_state:
            logger.info("Applying DeepFilter noise reduction")
            try:
                audio, _ = load_audio(str(audio_path), sr=self.df_state.sr())
                enhanced_audio = enhance(self.df_model, self.df_state, audio)
                save_audio(str(enhanced_path), enhanced_audio, self.df_state.sr())
            except Exception as e:
                logger.warning(f"DeepFilter failed, using original audio: {e}")
                enhanced_path = audio_path
        else:
            logger.info("DeepFilter not available, skipping noise reduction")
            enhanced_path = audio_path
        
        # Normalize volume
        target_dbfs = self.calculate_voice_volume(music_proportion)
        logger.info(f"Normalizing audio to {target_dbfs} dBFS")
        
        try:
            audio_segment = AudioSegment.from_file(str(enhanced_path))
            change = target_dbfs - audio_segment.dBFS
            normalized = audio_segment.apply_gain(change)
            normalized.export(str(normalized_path), format="wav")
            logger.info(f"Audio normalized and saved to {normalized_path}")
            
        except Exception as e:
            raise VideoProcessingError(f"Failed to normalize audio: {e}")
        
        return enhanced_path, normalized_path
    
    def calculate_voice_volume(self, music_proportion: float) -> float:
        """Calculate target volume for voice based on music proportion."""
        if music_proportion == 1.0:
            return 0  # Music only, no voice adjustment needed
        elif music_proportion > 0.5:
            ratio = (1 - music_proportion) / music_proportion
            return self.calculate_music_volume_adjustment(ratio) + 10
        else:
            return self.config.target_voice_volume
    
    def calculate_music_volume_adjustment(self, ratio: float) -> float:
        """Calculate volume adjustment for music based on voice-to-music ratio."""
        return self.config.base_music_volume - (20 * (1 - ratio) * (1 - ratio))
    
    def recolor_video(self, video_path: Path, reference_image_path: Optional[Path] = None) -> Path:
        """Apply color correction to video using histogram matching."""
        self.validate_file(video_path, "Video file")
        
        if not reference_image_path or not reference_image_path.exists():
            logger.info("No reference image provided or found, skipping color correction")
            return video_path
        
        logger.info(f"Applying color correction using reference: {reference_image_path}")
        
        # Load reference image
        try:
            reference_img = cv2.imread(str(reference_image_path))
            if reference_img is None:
                raise ValueError("Could not load reference image")
        except Exception as e:
            logger.warning(f"Failed to load reference image: {e}")
            return video_path
        
        # Get video properties
        props = self.get_video_properties(video_path)
        output_path = self.temp_dir / f"{video_path.stem}_recolored.mp4"
        
        # Process video
        vidcap = cv2.VideoCapture(str(video_path))
        if not vidcap.isOpened():
            raise VideoProcessingError(f"Could not open video: {video_path}")
        
        try:
            # Setup video writer
            fourcc = cv2.VideoWriter_fourcc(*'mp4v')
            out = cv2.VideoWriter(
                str(output_path),
                fourcc,
                props['fps'],
                (props['width'], props['height'])
            )
            
            frame_count = 0
            while True:
                ret, frame = vidcap.read()
                if not ret:
                    break
                
                # Apply histogram matching
                try:
                    corrected_frame = match_histograms(frame, reference_img, channel_axis=-1)
                    out.write(corrected_frame.astype(np.uint8))
                except Exception as e:
                    logger.warning(f"Failed to process frame {frame_count}: {e}")
                    out.write(frame)  # Use original frame
                
                frame_count += 1
                if frame_count % 100 == 0:
                    logger.info(f"Processed {frame_count} frames")
            
            logger.info(f"Color correction completed. Processed {frame_count} frames")
            
        except Exception as e:
            raise VideoProcessingError(f"Failed during video recoloring: {e}")
        finally:
            vidcap.release()
            out.release()
            cv2.destroyAllWindows()
        
        return output_path
    
    def replace_video_audio(self, video_path: Path, audio_path: Path, output_path: Path) -> None:
        """Replace video audio with processed audio."""
        try:
            self.validate_file(video_path, "Video file")
            self.validate_file(audio_path, "Audio file")
            
            logger.info(f"Replacing audio in video: {video_path}")
            video_input = ff.input(str(video_path))
            audio_input = ff.input(str(audio_path))
            
            (
                ff.concat(video_input, audio_input, v=1, a=1)
                .output(str(output_path))
                .run(overwrite_output=True, quiet=True)
            )
            logger.info(f"Video with new audio saved to: {output_path}")
            
        except ff.Error as e:
            error_msg = e.stderr.decode('utf-8') if e.stderr else "Unknown FFmpeg error"
            raise VideoProcessingError(f"Failed to replace audio: {error_msg}")
    
    def apply_highpass_filter(self, audio_path: Path, output_path: Path) -> None:
        """Apply highpass filter to audio file."""
        try:
            self.validate_file(audio_path, "Audio file")
            
            logger.info(f"Applying highpass filter to {audio_path}")
            y, sr = librosa.load(str(audio_path), sr=None)
            
            # Design highpass filter
            nyq = 0.5 * sr
            norm_cutoff = self.config.highpass_cutoff / nyq
            b, a = butter(self.config.highpass_order, norm_cutoff, btype='high', analog=False)
            
            # Apply filter
            filtered = lfilter(b, a, y)
            
            # Save filtered audio
            sf.write(str(output_path), filtered, sr)
            logger.info(f"Highpass filtered audio saved to: {output_path}")
            
        except Exception as e:
            raise VideoProcessingError(f"Failed to apply highpass filter: {e}")
    
    def add_background_music(self, video_path: Path, music_path: Path, 
                           music_proportion: float, output_path: Path) -> None:
        """Add background music to video with specified proportion."""
        if music_proportion == 0:
            # No music, just copy the video
            logger.info("No background music requested, copying video")
            try:
                (
                    ff.input(str(video_path))
                    .output(str(output_path), vcodec='copy', acodec='copy')
                    .run(overwrite_output=True, quiet=True)
                )
            except ff.Error as e:
                raise VideoProcessingError(f"Failed to copy video: {e}")
            return
        
        self.validate_file(video_path, "Video file")
        self.validate_file(music_path, "Music file")
        
        # Get video duration
        props = self.get_video_properties(video_path)
        duration_ms = int(props['duration'] * 1000)
        
        # Process music
        temp_music_path = self.temp_dir / f"processed_music_{music_path.stem}.wav"
        
        # Apply highpass filter if music proportion is low
        if music_proportion <= 0.5:
            logger.info("Applying highpass filter to music")
            self.apply_highpass_filter(music_path, temp_music_path)
        else:
            temp_music_path = music_path
        
        # Adjust music volume and duration
        try:
            logger.info("Processing background music")
            music_audio = AudioSegment.from_file(str(temp_music_path))
            
            # Trim or loop music to match video duration
            if len(music_audio) > duration_ms:
                music_audio = music_audio[:duration_ms]
            elif len(music_audio) < duration_ms:
                # Loop the music to match duration
                loops_needed = (duration_ms // len(music_audio)) + 1
                music_audio = music_audio * loops_needed
                music_audio = music_audio[:duration_ms]
            
            # Adjust volume
            if music_proportion == 1.0:
                target_dbfs = self.config.target_voice_volume
            else:
                ratio = music_proportion / (1 - music_proportion)
                target_dbfs = self.calculate_music_volume_adjustment(ratio)
                if music_proportion > 0.5:
                    target_dbfs = self.config.target_voice_volume
            
            change = target_dbfs - music_audio.dBFS
            normalized_music = music_audio.apply_gain(change)
            
            # Save processed music
            final_music_path = self.temp_dir / "final_music.wav"
            normalized_music.export(str(final_music_path), format="wav")
            
        except Exception as e:
            raise VideoProcessingError(f"Failed to process music: {e}")
        
        # Mix audio
        try:
            logger.info("Mixing video audio with background music")
            video_input = ff.input(str(video_path))
            music_input = ff.input(str(final_music_path))
            
            merged_audio = ff.filter([video_input.audio, music_input], 'amix')
            
            (
                ff.concat(video_input, merged_audio, v=1, a=1)
                .output(str(output_path))
                .run(overwrite_output=True, quiet=True)
            )
            logger.info(f"Final video with background music saved to: {output_path}")
            
        except ff.Error as e:
            error_msg = e.stderr.decode('utf-8') if e.stderr else "Unknown FFmpeg error"
            raise VideoProcessingError(f"Failed to mix audio: {error_msg}")
    
    def process_video(self, input_video: Path, output_video: Path,
                     music_file: Optional[Path] = None, music_proportion: float = 0.0,
                     reference_image: Optional[Path] = None) -> None:
        """
        Main processing pipeline for video enhancement.
        
        Args:
            input_video: Path to input video file
            output_video: Path to output video file
            music_file: Optional path to background music file
            music_proportion: Music volume proportion (0.0 = no music, 1.0 = music only)
            reference_image: Optional path to reference image for color correction
        """
        try:
            logger.info("Starting video processing pipeline")
            
            # Validate inputs
            self.validate_file(input_video, "Input video")
            if music_file and music_proportion > 0:
                self.validate_file(music_file, "Music file")
            
            # Step 1: Extract audio
            audio_file = self.temp_dir / "extracted_audio.wav"
            self.extract_audio_from_video(input_video, audio_file)
            
            # Step 2: Process audio (denoise and normalize)
            if music_proportion < 1.0:  # Only process if we need voice audio
                _, normalized_audio = self.denoise_and_normalize_audio(audio_file, music_proportion)
            else:
                normalized_audio = music_file  # Use music as main audio
            
            # Step 3: Color correction
            recolored_video = self.recolor_video(input_video, reference_image)
            
            # Step 4: Replace audio in recolored video
            video_with_new_audio = self.temp_dir / "video_with_processed_audio.mp4"
            self.replace_video_audio(recolored_video, normalized_audio, video_with_new_audio)
            
            # Step 5: Add background music
            if music_file and music_proportion > 0:
                self.add_background_music(video_with_new_audio, music_file, music_proportion, output_video)
            else:
                # No music, just copy the processed video
                try:
                    (
                        ff.input(str(video_with_new_audio))
                        .output(str(output_video), vcodec='copy', acodec='copy')
                        .run(overwrite_output=True, quiet=True)
                    )
                except ff.Error as e:
                    raise VideoProcessingError(f"Failed to copy final video: {e}")
            
            logger.info(f"Video processing completed successfully: {output_video}")
            
        except Exception as e:
            logger.error(f"Video processing failed: {e}")
            raise
    
    def cleanup_temp_files(self) -> None:
        """Clean up temporary files."""
        try:
            import shutil
            if self.temp_dir.exists():
                shutil.rmtree(self.temp_dir)
                logger.info("Temporary files cleaned up")
        except Exception as e:
            logger.warning(f"Failed to clean up temporary files: {e}")


def demo():
    """Example usage of the VideoProcessor."""
    # Configuration
    config = ProcessingConfig(
        audio_sample_rate=48000,
        target_voice_volume=-20,
        base_music_volume=-32
    )
    
    # Create processor
    processor = VideoProcessor(config)
    
    try:
        # Example processing
        vid_name = "2tu"
        input_video = Path("./testvids/" + vid_name + ".mp4")
        output_video = Path("./output/final_processed_video_" + vid_name + ".mp4")
        music_file = Path("./hyper_camelot.wav")
        # reference_image = Path("./02-histogram.jpg")
        
        # Ensure output directory exists
        output_video.parent.mkdir(exist_ok=True, parents=True)
        
        # Process video
        processor.process_video(
            input_video=input_video,
            output_video=output_video,
            music_file=music_file,
            music_proportion=0.8,
            reference_image=""
        )
        
    except Exception as e:
        logger.error(f"Processing failed: {e}")
    finally:
        # Clean up
        processor.cleanup_temp_files()


if __name__ == "__main__":
    demo()