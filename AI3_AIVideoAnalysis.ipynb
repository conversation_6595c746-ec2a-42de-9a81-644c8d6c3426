#%%
import ollama
import cv2
import base64

from df.enhance import enhance, init_df, load_audio, save_audio
from pydub import AudioSegment
import torchaudio
import cv2
import ffmpeg as ff
import whisper
import os

yes_prop = 1.5
system_prompt = 'Only respond with the description. No filler or introduction text. Do not repeat yourself.' 'You are a video analysis AI. You give concise but complete descriptions of images presented to you.'' Do not ''hallucinate. Do not lie about what you see. Do not add more details to your description. You are truthful, ''complete, concise, and effective at describing scenes and what to avoid doing to be able to recreate them. The recreation part is super important.'' Seperate every possible finding into different bullet points. Only base your answers off of the images given. That\'s wrong, try again.'
# system_prompt = "you are a video analysis AI assistant. Be concise, do not comment on methods - focus on results."

def extract_audio_from_video(video_path, audio_path):
    try:
        (
            ff
            .input(video_path)
            .output(audio_path, acodec='pcm_s16le', ac=2, ar='48000')  # WAV format, stereo, 48 kHz
            .overwrite_output()
            .run(capture_stdout=True, capture_stderr=True)
        )
    except ff.Error as e:
        print("FFmpeg stdout:", e.stdout.decode('utf8'))
        print("FFmpeg stderr:", e.stderr.decode('utf8'))
        raise e

def retarget_audio(ratio):
    return  -32 - (20 * (1 - ratio) * (1 - ratio))

def get_response_content(request, frames, model='gemma3:12b'):
    
    try:
        response = ollama.chat(
                        model = model,
                        messages = [
                            {
                                'role': 'user',
                                'content': request['content'],
                                'images' : frames
                            },
                        ],
                    )
        return response['message']['content']
    except KeyError:
        print("Content not found in request")
        return ""

def denoise_and_normalise(audio_name, music_proportion):

    if not os.path.exists(audio_name):
        print(f"Audio file '{audio_name}' not found.")
        return ""

    torchaudio.set_audio_backend('soundfile')

    # https://paperswithcode.com/paper/deepfilternet-perceptually-motivated-real#code
    # Load default model
    model, df_state, _ = init_df()
    audio_path = audio_name

    audio, _ = load_audio(audio_path, sr=df_state.sr())
    enhanced = enhance(model, df_state, audio)
    save_audio(audio_name + "_enhanced_deepfilter.wav", enhanced, df_state.sr())

    # Audio rebalancing - basically just change volume back -> music to voice ratio handled with existing pipeline
    if (music_proportion == 1):
        target_dBFS = 0
    else :
        target_dBFS = -20
        if (music_proportion > 0.5):
            ratio =  ((1 - music_proportion)/ (music_proportion))
            target_dBFS = retarget_audio(ratio) + 10

    audio = AudioSegment.from_file(audio_name + "_enhanced_deepfilter.wav")
    change = target_dBFS - audio.dBFS
    normalized = audio.apply_gain(change)
    normalized.export(audio_name + "_normalized_deepfilter.wav", format="wav")

    enhanced_n= audio_name + "_enhanced_deepfilter.wav"
    normalized_n = audio_name + "_normalized_deepfilter.wav"
    return enhanced_n, normalized_n

def convert_frame_base64(image):
    success, encoded_image = cv2.imencode('.png', image)
    if not success:
        raise RuntimeError("Failed to encode image.")
    
    image_base64 = base64.b64encode(encoded_image).decode('utf-8')
    return image_base64

def extract_bullets(raw_res):
    split = raw_res.splitlines()
    to_be_rm = []
    for i in range(len(split)):
        if (len(split[i].lstrip()) == 0 or split[i].lstrip()[0] != '*' or (len(split[i].lstrip()) >= 2 and split[i].lstrip()[0] == '*' and split[i].lstrip()[1] == '*')):
            to_be_rm.insert(0, i)
    for i in to_be_rm:
        split.pop(i)
    
    return split

def extract_frames_from_vid(vid_file):
    if not os.path.exists(vid_file):
        print(f"Video file '{vid_file}' not found.")
        return []

    vidcap = cv2.VideoCapture(vid_file)
    frames = []
    frame_nb = 0
    nb_frames_per_vid = 7

    frame_count = int(vidcap.get(cv2.CAP_PROP_FRAME_COUNT))
    frame_freq = round(frame_count / nb_frames_per_vid)

    success, image = vidcap.read()
    while success:
        if frame_nb % frame_freq == 0:

            if not success:
                raise RuntimeError("Failed to read frame from video.")

            image_base64 = convert_frame_base64(image)
            frames.append(image_base64)
        success, image = vidcap.read()
        frame_nb += 1
    return frames

#%%
import ollama
import cv2
import base64
import os
from typing import List, Tuple, Optional

from df.enhance import enhance, init_df, load_audio, save_audio
from pydub import AudioSegment
import torchaudio
import cv2
import ffmpeg as ff
import whisper

# Constants
FRAMES_PER_VIDEO = 7
BASE_TARGET_DBFS = -32
VOICE_TARGET_DBFS = -20
MUSIC_TARGET_DBFS = 0
GAIN_ADJUSTMENT = 10

# Fixed system prompt with proper string concatenation
system_prompt = ('Only respond with the description. No filler or introduction text. '
                'Do not repeat yourself. You are a video analysis AI. You give concise '
                'but complete descriptions of images presented to you. Do not hallucinate. '
                'Do not lie about what you see. Do not add more details to your description. '
                'You are truthful, complete, concise, and effective at describing scenes '
                'and what to avoid doing to be able to recreate them. The recreation part '
                'is super important. Separate every possible finding into different bullet '
                'points. Only base your answers off of the images given.')

def extract_audio_from_video(video_path, audio_path):
    """Extract audio from video file using FFmpeg."""
    if not isinstance(video_path, str) or not video_path:
        raise ValueError("video_path must be a non-empty string")
    if not isinstance(audio_path, str) or not audio_path:
        raise ValueError("audio_path must be a non-empty string")
    
    if not os.path.exists(video_path):
        raise FileNotFoundError(f"Video file '{video_path}' not found")
    
    try:
        (
            ff
            .input(video_path)
            .output(audio_path, acodec='pcm_s16le', ac=2, ar='48000')  # WAV format, stereo, 48 kHz
            .overwrite_output()
            .run(capture_stdout=True, capture_stderr=True)
        )
    except ff.Error as e:
        print("FFmpeg stdout:", e.stdout.decode('utf8'))
        print("FFmpeg stderr:", e.stderr.decode('utf8'))
        raise e

def retarget_audio(ratio):
    """Calculate target dBFS based on music-to-voice ratio."""
    if not isinstance(ratio, (int, float)):
        raise ValueError("ratio must be a number")
    return BASE_TARGET_DBFS - (20 * (1 - ratio) * (1 - ratio))

def get_response_content(request, frames, model='gemma3:12b'):
    """Get response from Ollama model with proper error handling."""
    if not isinstance(request, dict) or 'content' not in request:
        print("Invalid request format: missing 'content' key")
        return ""
    
    if not isinstance(frames, list):
        print("Frames must be a list")
        return ""
    
    try:
        response = ollama.chat(
            model=model,
            messages=[
                {
                    'role': 'user',
                    'content': request['content'],
                    'images': frames
                },
            ],
        )
        
        if 'message' not in response or 'content' not in response['message']:
            print("Unexpected response format from Ollama")
            return ""
            
        return response['message']['content']
        
    except KeyError as e:
        print(f"Content not found in request: {e}")
        return ""
    except Exception as e:
        print(f"Error getting response from Ollama: {e}")
        return ""

def denoise_and_normalise(audio_name, music_proportion):
    """Denoise and normalize audio using DeepFilter."""
    if not isinstance(audio_name, str) or not audio_name:
        print("Invalid audio_name parameter")
        return "", ""
    
    if not isinstance(music_proportion, (int, float)) or not 0 <= music_proportion <= 1:
        print("music_proportion must be a number between 0 and 1")
        return "", ""

    if not os.path.exists(audio_name):
        print(f"Audio file '{audio_name}' not found.")
        return "", ""

    try:
        torchaudio.set_audio_backend('soundfile')

        # Load default model
        model, df_state, _ = init_df()
        audio_path = audio_name

        audio, _ = load_audio(audio_path, sr=df_state.sr())
        enhanced = enhance(model, df_state, audio)
        
        enhanced_filename = audio_name + "_enhanced_deepfilter.wav"
        save_audio(enhanced_filename, enhanced, df_state.sr())

        # Audio rebalancing - music to voice ratio handling
        if music_proportion == 1:
            target_dBFS = MUSIC_TARGET_DBFS
        else:
            target_dBFS = VOICE_TARGET_DBFS
            if music_proportion > 0.5:
                ratio = (1 - music_proportion) / music_proportion
                target_dBFS = retarget_audio(ratio) + GAIN_ADJUSTMENT

        audio = AudioSegment.from_file(enhanced_filename)
        change = target_dBFS - audio.dBFS
        normalized = audio.apply_gain(change)
        
        normalized_filename = audio_name + "_normalized_deepfilter.wav"
        normalized.export(normalized_filename, format="wav")

        return enhanced_filename, normalized_filename
        
    except Exception as e:
        print(f"Error in denoise_and_normalise: {e}")
        return "", ""

def convert_frame_base64(image):
    """Convert OpenCV image to base64 string."""
    if image is None:
        raise ValueError("Image cannot be None")
    
    success, encoded_image = cv2.imencode('.png', image)
    if not success:
        raise RuntimeError("Failed to encode image.")
    
    image_base64 = base64.b64encode(encoded_image).decode('utf-8')
    return image_base64

def extract_bullets(raw_res):
    """Extract bullet points from raw response text."""
    if not isinstance(raw_res, str):
        return []
    
    split = raw_res.splitlines()
    bullets = []
    
    for line in split:
        stripped = line.lstrip()
        # Check if line starts with '*' but not '**'
        if (stripped and 
            stripped[0] == '*' and 
            not (len(stripped) >= 2 and stripped[1] == '*')):
            bullets.append(line)
    
    return bullets

def extract_frames_from_vid(vid_file):
    """Extract frames from video file with proper error handling."""
    if not isinstance(vid_file, str) or not vid_file:
        print("Invalid video file path")
        return []
    
    if not os.path.exists(vid_file):
        print(f"Video file '{vid_file}' not found.")
        return []

    vidcap = None
    try:
        vidcap = cv2.VideoCapture(vid_file)
        if not vidcap.isOpened():
            print(f"Failed to open video file: {vid_file}")
            return []
        
        frames = []
        frame_nb = 0

        frame_count = int(vidcap.get(cv2.CAP_PROP_FRAME_COUNT))
        if frame_count <= 0:
            print("Invalid frame count or empty video")
            return []
            
        # Ensure frame_freq is at least 1 to avoid division by zero
        frame_freq = max(1, round(frame_count / FRAMES_PER_VIDEO))

        success, image = vidcap.read()
        while success:
            if frame_nb % frame_freq == 0:
                try:
                    image_base64 = convert_frame_base64(image)
                    frames.append(image_base64)
                except Exception as e:
                    print(f"Error converting frame {frame_nb} to base64: {e}")
                    
            success, image = vidcap.read()
            frame_nb += 1
            
        return frames
        
    except Exception as e:
        print(f"Error extracting frames from video: {e}")
        return []
    finally:
        if vidcap is not None:
            vidcap.release()
#%%
def extract_audio_from_vid(vid_file):
    audio_file = "./tmp/02_audio.wav"

    extract_audio_from_video(vid_file, audio_file)
    _, normalized = denoise_and_normalise(audio_file, 0) # cleanup and re-volume
    
    model = whisper.load_model("turbo")
    result = model.transcribe(normalized)
    print(result["text"])
    return result["text"]
#%%

def describe_ppl_identity(frames, desc):     
    if (len(desc) == 0): return ""
    
    request = {'content' : 'Give this description of the following video: '
                     + desc + ' give a description of the People & Identity, meaning:\n '
                            'Covers who appears in the clip.'
                            'Number of people'
                            'Age group (child, teen, adult, senior)'
                            'Gender presentation'
                            'Specific personas (e.g., Hans, Laila)'
                            'Costume / outfit match'}

    # TODO replace with AWS bedrock
    response = get_response_content(request, frames, 'gemma3:4b')
            
    print(response + "\n\n")
    return True, response
#%%
def describe_emotions_expressions(frames, desc):     
    if (len(desc) == 0): return ""
    request = {
        'content' :  'Give this description of the following video: '
                     + desc + ' give a description of the Emotions & Expressions, meaning:\n '
                        'Covers how people appear emotionally.'
                            'Facial expression (e.g., smiling, angry)'
                            'Mood or energy (e.g., enthusiastic, calm)'
                            'Group atmosphere (e.g., celebrating together)'
                            '5 points maximum where each point is 10 words or less. Start all points with *. Do not comment on instructions.'
    }
    # TODO replace with AWS bedrock
    response = get_response_content(request, frames, 'gemma3:4b')
            
    print(response + "\n\n")
    return True, response
#%%
def describe_branding(frames, desc):     
    if (len(desc) == 0): return ""
    request = {
         'content' : 'Give this description of the following video: '
                     + desc + ' give a description of the Brand Visibility, meaning:\n '
                        'Covers visibility of brand elements.\n'
                            'Logo (on shirt, wall, product)\n'
                            'Brand color or pattern if a brand is present\n'
                            'Slogan / tagline appearance\n'
                            'Only mention branding and brand related elements'
                            '5 points maximum where each point is 10 words or less. Start all points with *. Do not comment on instructions.'
    }

    # TODO replace with AWS bedrock
    response = get_response_content(request, frames, 'gemma3:4b')
            
    print(response + "\n\n")
    return True, response
#%%
def describe_prod_place(frames, desc):     
    if (len(desc) == 0): return ""
    request =   {
                    'content' : 'Give this description of the following video: '
                         + desc + ' give a description of the Product Placement, meaning:\n '
                        'Covers how branded or featured products are shown.'
                            'Product presence'
                            'Product usage'
                            'Label visibility'
                            'Hero framing (centered, lit)'
                            '5 points maximum where each point is 10 words or less. Start all points with *. Do not comment on instructions.'
                }
    
    # TODO replace with AWS bedrock
    response = get_response_content(request, frames, 'gemma3:4b')
    
    print(response + "\n\n")
    return True, response
#%%
def describe_objs_props(frames, desc):     
    if (len(desc) == 0): return ""
    request =   {
                    'content' :  'Give this description of the following video: '
                         + desc + ' give a description of the Objects & Props, meaning:\n '
                            'Covers use or presence of non-brand items. Everything that is not a brand or product.'
                            'Presence of required item (e.g., book, cup)'
                            'Interaction with item (e.g., open door, hold sign)'
                            'Only focus on objects in the scene. Do not detail major background or natural elements. ' 
                            '5 points maximum where each point is 10 words or less. Start all points with *. Do not comment on instructions.'
                }
    
    # TODO replace with AWS bedrock
    response = get_response_content(request, frames, 'gemma3:4b')
            
    print(response + "\n\n")
    return True, response
#%%
def describe_env_background(frames, desc):     
    if (len(desc) == 0): return ""
    request =   {
                    'content' :  'Give this description of the following video: '
                         + desc + ' give a description of the Environment & Background, meaning:\n '
                        'Covers where the scene takes place'
                            'Indoor / outdoor'
                            'Type of place (e.g., park, car, office)'
                            'Visible landmarks / decor'
                            'Cleanliness / relevance of background'
                            '5 points maximum where each point is 10 words or less. Start all points with *. Do not comment on instructions.'
                }
    
    # TODO replace with AWS bedrock
    response = get_response_content(request, frames, 'gemma3:4b')
            
    print(response + "\n\n")
    return True, response
#%%
def describe_lighting_time(frames, desc):     
    if (len(desc) == 0): return ""
    request =   {
                    'content' : 'Give this description of the following video: '
                         + desc + ' give a description of the Lighting & Time, meaning:\n '
                        'Covers the visual and temporal context. Split from Environment to avoid mixing space and lighting.'
                            'Time of day (e.g., night, golden hour)'
                            'Lighting condition (e.g., well-lit, moody))'
                            'Use of natural vs artificial light'
                            '5 points maximum where each point is 10 words or less. Start all points with *. Do not comment on instructions.'
                }
    
    # TODO replace with AWS bedrock
    response = get_response_content(request, frames, 'gemma3:4b')
            
    print(response + "\n\n")
    return True, response
#%%
def describe_nature_plants(frames, desc):     
    if (len(desc) == 0): return ""
    request =   {
                     'content' : 'Give this description of the following video: '
                     + desc + ' give a description of the Animals, Nature & Plants, meaning:\n '
                        'Covers presence of natural elements'
                        'Animal inclusion or exclusion'
                        'Specific plants (e.g., flowers, trees)'
                        'Environmental details (e.g., mountain, beach)'
                        '5 points maximum where each point is 10 words or less. Start all points with *. Do not comment on instructions.'
                }
    
    # TODO replace with AWS bedrock
    response = get_response_content(request, frames, 'gemma3:4b')
    
    print(response + "\n\n")
    return True, response
#%%
def describe_camera_framing(frames, desc):     
    if (len(desc) == 0): return ""
    request =   {
                     'content': 'Give this description of the following video: '
                     + desc + ' give a description of the Camera & Framing, meaning:\n '
                        'Covers how the video is shot.'
                            'Shot type (selfie, tripod)'
                            'Angle (low, eye-level, top)'
                            'Framing (close-up, wide shot)'
                            '5 points maximum where each point is 10 words or less. Start all points with *. Do not comment on instructions.'
                }
    
    # TODO replace with AWS bedrock
    response = get_response_content(request, frames, 'gemma3:4b')
    
    print(response + "\n\n")
    return True, response
#%%
def describe_text_graphics(frames, desc):     
    if (len(desc) == 0): return ""
    request =   {
                     'content': 'Give this description of the following video: '
                     + desc + ' give a description of the Text & Graphics on Screen, meaning:\n '
                    ' Covers added visuals or captured writing.'
                        'Text on clothing, objects, signs'
                        'Stickers / emoji / graphics'
                        'Required captions or subtitles'
                        'No-text zones (for brand protection)'
                        '5 points maximum where each point is 10 words or less. Start all points with *. Do not comment on instructions.'
                }
    
    # TODO replace with AWS bedrock
    response = get_response_content(request, frames, 'gemma3:4b')
            
    print(response + "\n\n")
    return True, response

#%%
def audio_inference(frames, transcript, desc):     
    if (len(desc) == 0): return ""
    request =   {
                     'content': 'Given this transcript of the following video: '
                     + transcript + ' and this text description of a video:' + desc + ' give a description of the Speech & Voice, meaning:\n '
                    ' Covers what is said and how. Audio-based and verbal — different from body movement.'
                        'Required sentence or phrase'
                        'Language used'
                        # 'Tone of voice'
                        # 'Clarity / intelligibility'
                        '5 points maximum where each point is 10 words or less. Start all points with *. Do not comment on instructions.'
                }
    
    # TODO replace with AWS bedrock
    response = get_response_content(request, frames, 'gemma3:4b')
            
    print(response + "\n\n")
    return True, response
#%%
def assign_importance_level(frames, elements, category):
    request =   {
                    'content': 'Given the following visual elements (' + str(elements) + ') in the following video, give each of them an importance'
                'level relative to the following category when it comes to recreating the essence of the video by other users'
                ': ' + category + '.\n Here are the categories:'
                'Required (Must be present in participant clip Full penalty if missing), '
                'Recommended (Should be present; improves score but not blocking Partial penalty if missing),'
                'Forbidden (Must NOT be present, Hard rejection if present),'
                'Not Relevant (Ignored for this scene No impact on scoring)'
                'Create one importance level per visual element. Keep it concise. \n'
                'Format it as a list in this format: * detailed element - ranking. Keep it short and sweet. Be lenient.'
                }
    
    # TODO replace with AWS bedrock
    response = get_response_content(request, frames)
   
    print(response + "\n\n")
    return response
#%%
def describe_instructions(frames):
    request =   {
                    'content':
                'Give simple instructions to recreate the idea of this video in a way that is accessible to all. Focus on dominant objects or people (when applicable) in the dominant view. '
                'Focus on required movements, required objects, framing, setting and background. Do not be overly precise on details - be more broad than precise. '
                'Use 35~50 words max never going over 400 characters. Do not add any introductory text. Do not add movement or people that are not present in the frames. Make sure the wording makes it easy to reproduce. '
                'Use gender neutral language. Write like you are giving commands rather than just being a description. '
                }
    
    # TODO replace with AWS bedrock
    response = get_response_content(request, frames)

    print(response + "\n\n")
    return response
#%%
def gen_title(frames, desc):     
    if (len(desc) == 0): return ""
    request =   {
                    'content':
                'Generate a descriptive title for the video based off of this description: ' + desc + '. Do not hallucinate. Use 5 words max and stay under 25 characters. Give one title,'
                ' no intro text, no options, no extras. Use gender neutral language. Be simple - don\'t wax poetic and use only'
                ' major important elements in the description and video for the title. Do not make up new information. Base yourself off of this descritption ' + desc
                }
    
    # TODO replace with AWS bedrock
    response = get_response_content(request, frames)

    print(response + "\n\n")
    return response
#%%

# vid = "germaniguess"
# vid_file = './testvids/'+ vid +'.mp4'
# with open("demofile-" + vid + ".txt", "a") as f:
#     # Extract vid frames
#     frames = extract_frames_from_vid(vid_file)

#     transcript = extract_audio_from_vid(vid_file)
#     f.write(transcript + "\n\n")
#     f.write("-------------------------------------" + "\n\n")

#     instruct_one_block = describe_instructions(frames)
#     f.write(instruct_one_block + "\n\n")

#     title = gen_title(frames, instruct_one_block)
#     f.write(title + "\n\n")

#     print("-----------------------------------------------------------------------------------------------------------------------------------")
#     print("Audio")
#     f.write("Audio\n\n")
#     success, res = audio_inference(frames, transcript, instruct_one_block)

#     bullets = []
#     if (success): 
#         bullets = extract_bullets(res)
#         print(bullets)

#     if (len(bullets) > 0):
#         f.write(str(bullets) + "\n\n")
#         importance_levels = assign_importance_level(frames, bullets, "Speech & Voice")
#         f.write(str(extract_bullets(importance_levels)) + "\n\n")
    
#     print("-----------------------------------------------------------------------------------------------------------------------------------")
#     print("Branding")
#     f.write("Branding\n\n")
#     success, res = describe_branding(frames, instruct_one_block)

#     bullets = []
#     if (success): 
#         bullets = extract_bullets(res)
#         print(bullets)

#     if (len(bullets) > 0):
#         f.write(str(bullets) + "\n\n")
#         importance_levels = assign_importance_level(frames, bullets, "Branding")
#         f.write(str(extract_bullets(importance_levels)) + "\n\n")
        

   
#     print("-----------------------------------------------------------------------------------------------------------------------------------")
#     print("Camera and Framing")
#     f.write("Camera and Framing\n\n")
#     success, res = describe_camera_framing(frames, instruct_one_block)

#     bullets = []
#     if (success): 
#         bullets = extract_bullets(res)
    
#     if (len(bullets) > 0):
#         f.write(str(bullets) + "\n\n")
#         importance_levels = assign_importance_level(frames, bullets, "Camera Framing")
#         f.write(str(extract_bullets(importance_levels)) + "\n\n")

#     print("-----------------------------------------------------------------------------------------------------------------------------------")
#     print("Emotions and Expressions")
#     f.write("Emotions and Expressions\n\n")
#     success, res = describe_emotions_expressions(frames, instruct_one_block)

#     bullets = []
#     if (success): 
#         bullets = extract_bullets(res)

#     if (len(bullets) > 0):
#         f.write(str(bullets) + "\n\n")
#         importance_levels = assign_importance_level(frames, bullets, "Emotions and Expressions")
#         f.write(str(extract_bullets(importance_levels)) + "\n\n")

#     print("-----------------------------------------------------------------------------------------------------------------------------------")
#     print("Environment and Background")
#     f.write("Environment and Background\n\n")
#     success, res = describe_env_background(frames, instruct_one_block)

#     bullets = []
#     if (success): 
#         bullets = extract_bullets(res)

#     if (len(bullets) > 0):
#         f.write(str(bullets) + "\n\n")
#         importance_levels = assign_importance_level(frames, bullets, "Environment and Background")
#         f.write(str(extract_bullets(importance_levels)) + "\n\n")

#     print("-----------------------------------------------------------------------------------------------------------------------------------")
#     print("Lighting and Time")
#     f.write("Lighting and Time\n\n")
#     success, res = describe_lighting_time(frames, instruct_one_block)

#     bullets = []
#     if (success): 
#         bullets = extract_bullets(res)

#     if (len(bullets) > 0):
#         f.write(str(bullets) + "\n\n")
#         importance_levels = assign_importance_level(frames, bullets, "Lighting and Time")
#         f.write(str(extract_bullets(importance_levels)) + "\n\n")

#     print("-----------------------------------------------------------------------------------------------------------------------------------")
#     print("Nature and Plants")
#     f.write("Nature and Plants\n\n")
#     success, res = describe_nature_plants(frames, instruct_one_block)

#     bullets = []
#     if (success): 
#         bullets = extract_bullets(res)

#     if (len(bullets) > 0):
#         f.write(str(bullets) + "\n\n")
#         importance_levels = assign_importance_level(frames, bullets, "Nature and Plants")
#         f.write(str(extract_bullets(importance_levels)) + "\n\n")

#     print("-----------------------------------------------------------------------------------------------------------------------------------")
#     print("Objects and Props")
#     f.write("Objects and Prop \n\n")
#     success, res = describe_objs_props(frames, instruct_one_block)

#     bullets = []
#     if (success): 
#         bullets = extract_bullets(res)

#     if (len(bullets) > 0):
#         f.write(str(bullets) + "\n\n")
#         importance_levels = assign_importance_level(frames, bullets, "Objects and Props")
#         f.write(str(extract_bullets(importance_levels)) + "\n\n")

#     print("-----------------------------------------------------------------------------------------------------------------------------------")
#     print("People and Identity")
#     f.write("People and Identity \n\n")
#     success, res = describe_ppl_identity(frames, instruct_one_block)
    

#     bullets = []
#     if (success): 
#         bullets = extract_bullets(res)

#     if (len(bullets) > 0):
#         f.write(str(bullets) + "\n\n")
#         importance_levels = assign_importance_level(frames, bullets, "People and Identity")
#         f.write(str(extract_bullets(importance_levels)) + "\n\n")

#     print("-----------------------------------------------------------------------------------------------------------------------------------")
#     print("Product Placement")
#     f.write("Product Placement\n\n")
#     success, res = describe_prod_place(frames, instruct_one_block)
    

#     bullets = []
#     if (success): 
#         bullets = extract_bullets(res)

#     if (len(bullets) > 0):
#         f.write(str(bullets) + "\n\n")
#         importance_levels = assign_importance_level(frames, bullets, "Product Placement")
#         f.write(str(extract_bullets(importance_levels)) + "\n\n")

#     print("-----------------------------------------------------------------------------------------------------------------------------------")
#     print("Text and Graphics")
#     f.write("Text and Graphics\n\n")
#     success, res = describe_text_graphics(frames, instruct_one_block)
    

#     bullets = []
#     if (success): 
#         bullets = extract_bullets(res)

#     if (len(bullets) > 0):
#         f.write(str(bullets) + "\n\n")
#         importance_levels = assign_importance_level(frames, bullets, "Text and Graphics")
#         f.write(str(extract_bullets(importance_levels)) + "\n\n")


# # # this is what we think is key, ask more details, have producer give more, really go for the essentials for each category
# # # have option for incerased details

# # # AI gen instructions if limited in space = focus on main points, AI gen insutruction (have option to gen more details) -> prod validates them -> AI gen criterion ponderation for compliance takes the description and gens criterions and puts em in right category for sub categories can ask for more criterion

# # # max character limit - needs to gen instructs with that max

# # # test clips with ppl dancing
# # # where they do an interview
# # # one where you see nature
# # # another with prominent branding
#%%
def evaluate_video(frames, description, transcript):
    if (len(description) == 0): return ""
    if (len(transcript) == 0): return ""
    request =   {
                     'content': 'Given the following video frames - which always come from a video - and this first description.'
                'Give a percentage by which they match this descripiton: ' + description + '.\n'
                '≥ 80% → Auto-approved, compliant'
                '60-79.9% → Sent to Producer for review - mostly compliant, but with some minor discrepensies'
                '< 60% → Auto-rejected - mostly non-compliant, major discrepensies.'
                'Always give feedback. Focus on background, required objects and required movements. '
                'Make sure you view these frames as being together in a video. '
                'Give an overall % for compliance with this description \"' + description + '\" given that this was said in the video (' + transcript + '). For interview style videos, use the transcript to evalute compliance.'
                ' Rate discrepensies in major movements, spoken words or background harshly, when applicable, taking away a lot of percent points. If they do not say what they have to say, autiomatically disqualify them. '
                'Be generous otherwise. A bit more generous when the videos match. If 80% matches, score it as 100% and extrapolate from there.'
                'Normalize the score to be out of 100 total. 50 to 100 words total. Make sure required text is contained in (' + transcript + ')\n'
                'End with \"Final score: X%\". It is okay if things aren\'t perfect. Always add justification. Do not go above 75 words. Derive your score from the justification. Do not hallucinate.'
                }
    
    # TODO replace with AWS bedrock
    response = get_response_content(request, frames)
    return response

def extract_compliance(description):
    if (len(description) == 0): return ""
    request =   {
                     'content': 'Respond only with the compliance percentage in this description: ' + description
                }
    
    # TODO replace with AWS bedrock
    response = get_response_content(request, [])
    return response

def evaluate_descs(frames, description):
    if (len(description) == 0): return ""
    request =   {
                    'content': 'Given the following video and this first description.'
                'Give a percentage by which they match this descripiton: ' + description + ''
                'MATCH THE TEXTUAL DESCRIPTIONS THE PICTURES ARE SMALL AIDS. If you like I will stab you. If you call a major component minor I will drown you.'
                }
    
    # TODO replace with AWS bedrock
    response = get_response_content(request, frames)
    return response
#%%
def run_compliance_test(vid):
    instruct_one_block = "Take a video of a notable landmark in your city."
    vid_file = './testvids/'+ vid +'.mp4'
    transcript = extract_audio_from_vid(vid_file)
    with open("compliancedemo-" + instruct_one_block + ".txt", "a") as f:
        f.write("-----------------------------------------------------------------------------------------------------------------------------------" + "\n\n")
        print("-----------------------------------------------------------------------------------------------------------------------------------")
        f.write(vid_file + "\n\n")
        f.write("Transcript: " + transcript + "\n\n")
        frames = extract_frames_from_vid(vid_file)
        f.write(instruct_one_block + "\n\n")
        print(instruct_one_block)
        eval = evaluate_video(frames, instruct_one_block, transcript)
        f.write(eval + "\n\n")
        print(eval)
        res = extract_compliance(eval)
        print(res)
        f.write(res + "\n\n")
        new_instruct = describe_instructions(frames)
        evaluate_descs(frames, instruct_one_block, new_instruct)

# run_compliance_test('2tu')
# run_compliance_test('beach-run')
# run_compliance_test('competition')
# run_compliance_test('computer')
# run_compliance_test('ecology')
# run_compliance_test('fashion')
# run_compliance_test('germaniguess')
# run_compliance_test('interview')
# run_compliance_test('slow-pan-mountain')
# run_compliance_test('spanish')
# run_compliance_test('thankyou')
# run_compliance_test('thankyouppl')
# run_compliance_test('up')
#%%
def describe_tips_tricks(desc):
    if (len(desc) == 0): return ""
    request =   {
                    'content':  'Give tips and tricks on how to recreate a video based on this description \'' + desc + '\' in a way that is accessible to all. Focus on elements in this description (' + desc + '). '
                'Focus on required movements, required objects, framing, setting and background. Remain as vague (or precise, as applicable) as the description. '
                'Instruct the person via tips and tricks on potential locations, targets, movements, etc. they can do, go to or see in their city to create a video mathing the description. Do not use a bullet point list. '
                'Use 35~50 words max never going over 400 characters. Do not add any introductory text. Make sure the wording makes it easy to reproduce. Use gender neutral language.'
                }
    
    # TODO replace with AWS bedrock
    response = get_response_content(request, [])
    return response
#%%
vid_file = './testvids/'+ 'slow-pan-mountain' +'.mp4'
instruct_one_block = "Throw your hands up as if you did the wave. Use a white background and use gymwear. Look happy."
describe_tips_tricks(instruct_one_block)
#%%
def describe_warnings(desc):
    if (len(desc) == 0): return ""
    request =   {
                'content':
                    'Give warnings on common mistakes when trying to create a video based on this description \'' + desc + '\' in a way that is accessible to all. Focus on elements in this description (' + desc + '). '
                    'Focus on required movements, required objects, framing, setting and background. Remain as vague (or precise, as applicable) as the description. '
                    'Warn the person via a short list of warnings on potential locations to avoid, movements to avoid, lighting scenarios that are sub-optimal, etc. Do not use a bullet point list. '
                    'Use 35~50 words max never going over 400 characters. Do not add any introductory text. Make sure the wording makes it easy to reproduce. Use gender neutral language.'
                }
    
    # TODO replace with AWS bedrock
    response = get_response_content(request, [])
    return response
#%%
vid_file = './testvids/'+ 'slow-pan-mountain' +'.mp4'
# instruct_one_block = "Look into the camera and say \'No to Racism\'"
describe_warnings(instruct_one_block)
#%%
def translate_txt(text, source, target):
    # list langs, split by left right and right left ordered by most used on mobile
    if (len(text) == 0): return ""
    if (len(source) == 0): return ""
    if (len(target) == 0): return ""
    request =   {
                'content':
                    'Translate this: ' + text + " from " + source + " to " + target + '. Translate without giving options. Offer the best translation with no additional text. Translate meaning.'
                }
    
    # TODO replace with AWS bedrock
    response = get_response_content(request, [])
    return response
#%%
# gen docs on how to use code interface
translate_txt("I like racism. Nvm, it's ass.", "English", "Spanish")
#%%
request = {}
get_response_content(request, [])
#%%
name_list = ['2tu', 'beach-run', 'competition', 'computer', 'ecology', 'fashion', 'germaniguess', 'interview', 'slow-pan-mountain', 'spanish', 'thankyou', 'thankyouppl', 'up']
for vid_name in name_list: 
    vid_file = './testvids/'+ vid_name +'.mp4'
    with open('./testvids/'+ vid_name + ".txt", "a") as f:
        frames = extract_frames_from_vid(vid_file)
        f.write(describe_instructions(frames))