#%%
# Video Matcher using Custom LLM Function with Updated Frame Extraction
# For Jupyter Notebook - Grandma-safe edition v3

import os
import ollama
from pathlib import Path
from typing import List, Dict, Tuple, Optional
import cv2
import re
from dataclasses import dataclass
import time
import base64

@dataclass
class VideoCandidate:
    """Data class for video candidates"""
    filename: str
    path: str
    description: str
    score: float
    file_size: int

def get_response_content(request, frames, model='gemma3:14b'):
    """Custom LLM function with enhanced error handling"""
    try:
        response = ollama.chat(
            model=model,
            messages=[
                {
                    'role': 'user',
                    'content': request['content'],
                    'images': frames
                },
            ],
        )
        return response['message']['content']
    except KeyError as e:
        print(f"Content not found in request: {e}")
        return ""
    except ConnectionError as e:
        print(f"Connection error with LLM: {e}")
        return ""
    except Exception as e:
        print(f"Unexpected error in LLM call: {e}")
        return ""

def extract_frames_from_vid(vid_file):
    """Extract frames from video and return as base64 strings"""
    if not os.path.exists(vid_file):
        print(f"Video file '{vid_file}' not found.")
        return []
     
    vidcap = cv2.VideoCapture(vid_file)
    frames = []
    frame_nb = 0
    nb_frames_per_vid = 7
     
    frame_count = int(vidcap.get(cv2.CAP_PROP_FRAME_COUNT))
    frame_freq = round(frame_count / nb_frames_per_vid)
     
    success, image = vidcap.read()
    while success:
        if frame_nb % frame_freq == 0:
             
            if not success:
                raise RuntimeError("Failed to read frame from video.")
             
            image_base64 = convert_frame_base64(image)
            frames.append(image_base64)
        success, image = vidcap.read()
        frame_nb += 1
    
    vidcap.release()
    return frames

def convert_frame_base64(image):
    """Convert CV2 image to base64 string"""
    success, encoded_image = cv2.imencode('.png', image)
    if not success:
        raise RuntimeError("Failed to encode image.")
         
    image_base64 = base64.b64encode(encoded_image).decode('utf-8')
    return image_base64

class VideoMatcher:
    def __init__(self, video_dir: str, descriptions_dir: str, model_name: str = "gemma3:14b"):
        """
        Initialize VideoMatcher for Jupyter Notebook
        
        Args:
            video_dir: Directory containing MP4 files
            descriptions_dir: Directory containing TXT description files  
            model_name: LLM model name (gemma3:14b, gemma3:12b, etc.)
        """
        self.video_dir = Path(video_dir)
        self.descriptions_dir = Path(descriptions_dir)
        self.model_name = model_name
        
        print(f"🎬 VideoMatcher initialized")
        print(f"📁 Video directory: {self.video_dir}")
        print(f"📄 Descriptions directory: {self.descriptions_dir}")
        print(f"🤖 Using model: {self.model_name}")
        
        # Test LLM connection
        self._test_llm_connection()
    
    def _test_llm_connection(self):
        """Test if LLM is working and model is available"""
        try:
            # Test the custom function
            test_request = {"content": "Say 'OK' if you can hear me"}
            test_response = get_response_content(test_request, [], self.model_name)
            
            if test_response:
                print(f"✅ LLM connected. Test response: {test_response[:50]}...")
            else:
                print("⚠️  LLM responded but with empty content")
            
        except Exception as e:
            print(f"❌ LLM connection failed: {e}")
            print("💡 Make sure ollama is installed and running")
            raise
    
    def get_video_files(self) -> List[Path]:
        """Get all MP4 files in video directory"""
        video_files = list(self.video_dir.glob("*.mp4"))
        print(f"📹 Found {len(video_files)} video files")
        return video_files
    
    def get_description(self, video_path: Path) -> str:
        """Get text description for a video file"""
        # Try multiple naming conventions
        possible_desc_files = [
            self.descriptions_dir / f"{video_path.stem}.txt",
            self.descriptions_dir / f"{video_path.name}.txt",
            self.descriptions_dir / f"{video_path.name.replace('.mp4', '.txt')}",
            self.descriptions_dir / f"{video_path.name.replace('.MP4', '.txt')}"
        ]
        
        for desc_file in possible_desc_files:
            if desc_file.exists():
                try:
                    content = desc_file.read_text(encoding='utf-8').strip()
                    if content:  # Make sure it's not empty
                        return content
                except Exception as e:
                    print(f"⚠️  Error reading {desc_file}: {e}")
                    continue
        
        # Fallback description
        return f"Video file: {video_path.name} (no description available)"
    
    def call_llm(self, prompt: str, frames: List = None) -> str:
        """Call LLM using the custom function"""
        if frames is None:
            frames = []
        
        request = {"content": prompt}
        response = get_response_content(request, frames, self.model_name)
        return response.strip() if response else "0"
    
    def score_video_description(self, description: str, user_prompt: str) -> float:
        """Score how well a video description matches the user prompt"""
        
        scoring_prompt = f"""You are a video search expert. Rate how well this video description matches the user's search request.

USER SEARCH: "{user_prompt}"

VIDEO DESCRIPTION: "{description}"

Rate the match from 0-100 where:
- 100 = Perfect match, exactly what user wants  
- 80-99 = Very good match, highly relevant
- 60-79 = Good match, somewhat relevant
- 40-59 = Moderate match, partially relevant  
- 20-39 = Poor match, barely relevant
- 0-19 = No match, completely irrelevant

Consider semantic meaning, not just keywords. A video about "ocean waves" matches "sea footage" even without exact words.

Respond with ONLY the number (0-100). No explanation needed.

SCORE:"""
        
        score_text = self.call_llm(scoring_prompt)
        
        # Extract number from response - be very robust
        try:
            # Look for any number in the response
            numbers = re.findall(r'\d+', score_text)
            if numbers:
                score = int(numbers[0])
                # Clamp to valid range
                score = max(0, min(100, score))
                return score / 100.0  # Normalize to 0-1
            else:
                print(f"⚠️  No number found in score response: '{score_text}'")
                return 0.0
        except Exception as e:
            print(f"⚠️  Error parsing score '{score_text}': {e}")
            return 0.0
    
    def score_video_with_frames(self, description: str, user_prompt: str, frames: List[str]) -> float:
        """Score video using both description and visual frames"""
        
        if not frames:
            return self.score_video_description(description, user_prompt)
        
        # Limit frames to avoid token limits
        # limited_frames = frames[:3]
        
        visual_scoring_prompt = f"""You are analyzing a video to see how well it matches a user's search request.

USER SEARCH: "{user_prompt}"

TEXT DESCRIPTION: "{description}"

You can also see {len(frames)} frames from this video. Analyze both the text description and the visual content.

Rate the overall match from 0-100 considering:
- How well the description matches the search
- How well the visual content matches the search  
- Overall relevance to what the user wants

Scoring scale:
- 100 = Perfect match (text + visuals exactly what user wants)
- 80-99 = Very good match (highly relevant)
- 60-79 = Good match (somewhat relevant)
- 40-59 = Moderate match (partially relevant)
- 20-39 = Poor match (barely relevant)
- 0-19 = No match (completely irrelevant)

Respond with ONLY the number (0-100). No explanation.

SCORE:"""
        
        score_text = self.call_llm(visual_scoring_prompt, frames)
        
        # Extract number from response
        try:
            numbers = re.findall(r'\d+', score_text)
            if numbers:
                score = int(numbers[0])
                score = max(0, min(100, score))
                return score / 100.0
            else:
                print(f"⚠️  No number in visual score: '{score_text}'")
                # Fallback to text-only scoring
                return self.score_video_description(description, user_prompt)
        except Exception as e:
            print(f"⚠️  Error parsing visual score: {e}")
            return self.score_video_description(description, user_prompt)
    
    def enhance_description_with_frames(self, video_path: Path, original_description: str) -> Tuple[str, List[str]]:
        """Enhance description using extracted frames and LLM vision"""
        try:
            print(f"  🖼️  Extracting frames for {video_path.name}...")
            frames = extract_frames_from_vid(str(video_path))
            
            if not frames or len(frames) == 0:
                print(f"  ⚠️  No frames extracted")
                return original_description, []
            
            print(f"  ✅ Extracted {len(frames)} frames")
            
            # Analyze frames with LLM vision
            frame_analysis = self._analyze_frames_with_vision(original_description, frames[:3])
            
            if frame_analysis and frame_analysis.strip():
                enhanced = f"{original_description}\n\nVisual analysis: {frame_analysis}"
                return enhanced, frames
            else:
                return original_description, frames
                
        except Exception as e:
            print(f"  ❌ Error enhancing description: {e}")
            return original_description, []
    
    def _analyze_frames_with_vision(self, description: str, frames: List[str]) -> str:
        """Analyze frames using LLM vision capabilities"""
        try:
            analysis_prompt = f"""Look at these video frames and enhance the existing description.

EXISTING DESCRIPTION: "{description}"

Analyze the visual content in these {len(frames)} frames and provide additional details about:
- Visual style and mood
- Colors and lighting
- Actions or movement visible
- Setting and environment
- Any notable visual elements

Keep your analysis brief (1-2 sentences) and focus on details that would help match this video to search queries.

VISUAL ANALYSIS:"""
            
            return self.call_llm(analysis_prompt, frames)
                
        except Exception as e:
            print(f"    ⚠️  Error in visual frame analysis: {e}")
            return ""
    
    def find_best_match(self, user_prompt: str, use_frames: bool = False, top_k: int = 5) -> Tuple[str, Dict]:
        """
        Find the video that best matches the user prompt
        
        Args:
            user_prompt: User's search query
            use_frames: Whether to enhance descriptions with frame analysis  
            top_k: Number of top candidates to return
            
        Returns:
            Tuple of (best_video_name, complete_results_dict)
        """
        print(f"\n🔍 Searching for: '{user_prompt}'")
        print("=" * 50)
        
        video_files = self.get_video_files()
        
        if not video_files:
            raise ValueError("❌ No MP4 files found in video directory")
        
        candidates = []
        
        for i, video_path in enumerate(video_files):
            print(f"📹 Processing {i+1}/{len(video_files)}: {video_path.name}")
            
            # Get description
            description = self.get_description(video_path)
            print(f"  📝 Description: {description[:100]}...")
            
            # Get score with or without frames
            if use_frames:
                print(f"  🖼️  Extracting and analyzing frames...")
                enhanced_description, frames = self.enhance_description_with_frames(video_path, description)
                score = self.score_video_with_frames(enhanced_description, user_prompt, frames)
                final_description = enhanced_description
            else:
                print(f"  🤖 Scoring with LLM...")
                score = self.score_video_description(description, user_prompt)
                final_description = description
            
            print(f"  📊 Score: {score:.3f}")
            
            # Create candidate
            candidate = VideoCandidate(
                filename=video_path.name,
                path=str(video_path),
                description=final_description,
                score=score,
                file_size=video_path.stat().st_size
            )
            candidates.append(candidate)
            print(f"  ✅ Done\n")
        
        # Sort by score (highest first)
        candidates.sort(key=lambda x: x.score, reverse=True)
        
        if not candidates:
            raise ValueError("❌ No suitable videos found")
        
        best_candidate = candidates[0]
        
        # Prepare complete results
        results = {
            "best_match": {
                "filename": best_candidate.filename,
                "path": best_candidate.path,
                "description": best_candidate.description,
                "score": best_candidate.score,
                "file_size": best_candidate.file_size
            },
            "top_candidates": [
                {
                    "rank": i + 1,
                    "filename": candidate.filename,
                    "score": candidate.score,
                    "description": candidate.description[:200] + "..." if len(candidate.description) > 200 else candidate.description,
                    "path": candidate.path
                }
                for i, candidate in enumerate(candidates[:top_k])
            ],
            "search_summary": {
                "query": user_prompt,
                "total_videos_analyzed": len(video_files),
                "best_score": best_candidate.score,
                "used_frame_analysis": use_frames
            }
        }
        
        # Print results
        print("🏆 RESULTS")
        print("=" * 50)
        print(f"🥇 Best match: {best_candidate.filename}")
        print(f"📊 Score: {best_candidate.score:.3f}")
        print(f"📝 Description: {best_candidate.description[:150]}...")
        print(f"\n📋 Top {min(top_k, len(candidates))} candidates:")
        
        for i, candidate in enumerate(candidates[:top_k]):
            emoji = ["🥇", "🥈", "🥉", "4️⃣", "5️⃣"][i] if i < 5 else f"{i+1}️⃣"
            print(f"  {emoji} {candidate.filename} (score: {candidate.score:.3f})")
        
        return best_candidate.filename, results


# Main API function
def find_best_video(prompt: str, video_dir: str = "videos", descriptions_dir: str = "descriptions", 
                   use_frames: bool = False, model_name: str = "gemma3:14b", top_k: int = 5) -> Tuple[str, Dict]:
    """
    🎯 Main API function - find best matching video
    
    Args:
        prompt: What you're looking for ("ocean waves", "celebration", etc.)
        video_dir: Folder with your MP4 files
        descriptions_dir: Folder with your TXT description files
        use_frames: Whether to analyze frames for better matching
        model_name: LLM model to use (gemma3:14b, gemma3:12b, etc.)
        
    Returns:
        (best_video_filename, complete_results_dict)
    """
    matcher = VideoMatcher(video_dir, descriptions_dir, model_name)
    return matcher.find_best_match(prompt, use_frames, top_k=top_k)


# Jupyter notebook demo
def demo():
    """Run a demo in Jupyter notebook"""
    print("🚀 VIDEO MATCHER DEMO")
    print("=" * 60)
    
    # Test with sample prompts
    # test_prompts = [
    #     "beautiful ocean scenery",
    #     "people celebrating and cheering", 
    #     "nature documentary footage",
    #     "sports action and movement"
    # ]
    # Test with sample prompts
    test_prompts = [
        "interview with my friend in a turban"
    ]
    
    for prompt in test_prompts:
        try:
            print(f"\n🎬 TESTING: '{prompt}'")
            print("-" * 40)
            
            video_name, results = find_best_video(
                prompt=prompt,
                video_dir="./testvids",  # Change to your video directory
                descriptions_dir="./testdescs",  # Change to your descriptions directory
                use_frames=True,  # Set to True to use visual analysis
                model_name="gemma3:12b",  # Use the model you prefer
                top_k=10
            )
            
            print(f"✅ SUCCESS: Found {video_name}")
            print(results)
            
        except Exception as e:
            print(f"❌ ERROR: {e}")

if __name__ == "__main__":
    # This runs the demo when executed as script
    # demo()

    try:
        print(f"🔍 Quick test searching for: Donne moi une vidéo où l'on voit quelqu'un être très joyeux.")
        video_name, results = find_best_video(
                prompt="Donne moi une vidéo où l'on voit quelqu'un être très joyeux.",
                video_dir="./testvids",  # Change to your video directory
                descriptions_dir="./testdescs",  # Change to your descriptions directory
                use_frames=True,  # Set to True to use visual analysis
                model_name="gemma3:12b",  # Use the model you prefer
                top_k=10
        )

        print(f"Best match: {video_name}")
        print(f"Score: {results['best_match']['score']:.3f}")
        print(f"Description: {results['best_match']['description'][:200]}...")
        
        print(results)
        
    except Exception as e:
        print(f"❌ Error in quick test: {e}")

    # select in user's lang
    # have it gen reqs for backend code exec -> sec risk?
    # backend filters clip by permission
    # dont always have enough clips to do each scenes
    # have direct person to scenes where clips are missing -> this can be done on backend
    # prio per lang
    # train ai on manual selection - check which elements you need and retrain
    # flag system for explicit, violent, or illicit content
    # send all clips > 0.8, order rest especially if no clips match
    # reglar algorithms wooo
#%%
# Video Matcher with Audio Analysis - Enhanced Edition
# Includes visual frames + audio transcription analysis

import os
import ollama
from pathlib import Path
from typing import List, Dict, Tuple, Optional
import cv2
import re
from dataclasses import dataclass
import time
import base64
import whisper
from df.enhance import enhance, init_df, load_audio, save_audio
from pydub import AudioSegment
import torchaudio
import cv2
import ffmpeg as ff

FRAMES_PER_VIDEO = 7
BASE_TARGET_DBFS = -32
VOICE_TARGET_DBFS = -20
MUSIC_TARGET_DBFS = 0
GAIN_ADJUSTMENT = 10

@dataclass
class VideoCandidate:
    """Data class for video candidates"""
    filename: str
    path: str
    description: str
    audio_transcript: str
    score: float
    file_size: int

def get_response_content(request, frames, model='gemma3:14b'):
    """Custom LLM function with enhanced error handling"""
    try:
        response = ollama.chat(
            model=model,
            messages=[
                {
                    'role': 'user',
                    'content': request['content'],
                    'images': frames
                },
            ],
        )
        return response['message']['content']
    except KeyError as e:
        print(f"Content not found in request: {e}")
        return ""
    except ConnectionError as e:
        print(f"Connection error with LLM: {e}")
        return ""
    except Exception as e:
        print(f"Unexpected error in LLM call: {e}")
        return ""

def extract_frames_from_vid(vid_file):
    """Extract frames from video and return as base64 strings"""
    if not os.path.exists(vid_file):
        print(f"Video file '{vid_file}' not found.")
        return []
     
    vidcap = cv2.VideoCapture(vid_file)
    frames = []
    frame_nb = 0
    nb_frames_per_vid = 7
     
    frame_count = int(vidcap.get(cv2.CAP_PROP_FRAME_COUNT))
    frame_freq = round(frame_count / nb_frames_per_vid)
     
    success, image = vidcap.read()
    while success:
        if frame_nb % frame_freq == 0:
             
            if not success:
                raise RuntimeError("Failed to read frame from video.")
             
            image_base64 = convert_frame_base64(image)
            frames.append(image_base64)
        success, image = vidcap.read()
        frame_nb += 1
    
    vidcap.release()
    return frames

def convert_frame_base64(image):
    """Convert CV2 image to base64 string"""
    success, encoded_image = cv2.imencode('.png', image)
    if not success:
        raise RuntimeError("Failed to encode image.")
         
    image_base64 = base64.b64encode(encoded_image).decode('utf-8')
    return image_base64

def extract_audio_from_video(video_path, audio_path):
    """Extract audio from video file using FFmpeg."""
    if not isinstance(video_path, str) or not video_path:
        raise ValueError("video_path must be a non-empty string")
    if not isinstance(audio_path, str) or not audio_path:
        raise ValueError("audio_path must be a non-empty string")
    
    if not os.path.exists(video_path):
        raise FileNotFoundError(f"Video file '{video_path}' not found")
    
    try:
        (
            ff
            .input(video_path)
            .output(audio_path, acodec='pcm_s16le', ac=2, ar='48000')  # WAV format, stereo, 48 kHz
            .overwrite_output()
            .run(capture_stdout=True, capture_stderr=True)
        )
    except ff.Error as e:
        print("FFmpeg stdout:", e.stdout.decode('utf8'))
        print("FFmpeg stderr:", e.stderr.decode('utf8'))
        raise e
    
def denoise_and_normalise(audio_name, music_proportion):
    """Denoise and normalize audio using DeepFilter."""
    if not isinstance(audio_name, str) or not audio_name:
        print("Invalid audio_name parameter")
        return "", ""
    
    if not isinstance(music_proportion, (int, float)) or not 0 <= music_proportion <= 1:
        print("music_proportion must be a number between 0 and 1")
        return "", ""

    if not os.path.exists(audio_name):
        print(f"Audio file '{audio_name}' not found.")
        return "", ""

    try:
        torchaudio.set_audio_backend('soundfile')

        # Load default model
        model, df_state, _ = init_df()
        audio_path = audio_name

        audio, _ = load_audio(audio_path, sr=df_state.sr())
        enhanced = enhance(model, df_state, audio)
        
        enhanced_filename = audio_name + "_enhanced_deepfilter.wav"
        save_audio(enhanced_filename, enhanced, df_state.sr())

        # Audio rebalancing - music to voice ratio handling
        if music_proportion == 1:
            target_dBFS = MUSIC_TARGET_DBFS
        else:
            target_dBFS = VOICE_TARGET_DBFS
            if music_proportion > 0.5:
                ratio = (1 - music_proportion) / music_proportion
                target_dBFS = retarget_audio(ratio) + GAIN_ADJUSTMENT

        audio = AudioSegment.from_file(enhanced_filename)
        change = target_dBFS - audio.dBFS
        normalized = audio.apply_gain(change)
        
        normalized_filename = audio_name + "_normalized_deepfilter.wav"
        normalized.export(normalized_filename, format="wav")

        return enhanced_filename, normalized_filename
        
    except Exception as e:
        print(f"Error in denoise_and_normalise: {e}")
        return "", ""



def extract_audio_from_vid(vid_file):
    """Extract and transcribe audio from video using Whisper"""
    try:
        # Import the required audio processing functions
        # Assuming these functions are available in your environment
        
        audio_file = "./tmp/02_audio.wav"
        
        # Create tmp directory if it doesn't exist
        os.makedirs("./tmp", exist_ok=True)
        
        # Extract audio from video
        extract_audio_from_video(vid_file, audio_file)
        
        # Clean up and normalize audio
        _, normalized = denoise_and_normalise(audio_file, 0)
        
        # Load Whisper model and transcribe
        model = whisper.load_model("turbo")
        result = model.transcribe(normalized)
        
        transcript = result["text"]
        print(f"🎵 Transcribed audio: {transcript[:100]}...")
        
        # Clean up temporary files
        try:
            os.remove(audio_file)
            if normalized != audio_file:
                os.remove(normalized)
        except:
            pass
        
        return transcript
        
    except Exception as e:
        print(f"⚠️  Audio extraction failed for {vid_file}: {e}")
        return ""

class VideoMatcher:
    def __init__(self, video_dir: str, descriptions_dir: str, model_name: str = "gemma3:14b", 
                 use_audio: bool = False):
        """
        Initialize VideoMatcher with optional audio analysis
        
        Args:
            video_dir: Directory containing MP4 files
            descriptions_dir: Directory containing TXT description files  
            model_name: LLM model name (gemma3:14b, gemma3:12b, etc.)
            use_audio: Whether to enable audio transcription analysis
        """
        self.video_dir = Path(video_dir)
        self.descriptions_dir = Path(descriptions_dir)
        self.model_name = model_name
        self.use_audio = use_audio
        
        print(f"🎬 VideoMatcher initialized")
        print(f"📁 Video directory: {self.video_dir}")
        print(f"📄 Descriptions directory: {self.descriptions_dir}")
        print(f"🤖 Using model: {self.model_name}")
        print(f"🎵 Audio analysis: {'Enabled' if use_audio else 'Disabled'}")
        
        # Initialize Whisper model if audio analysis is enabled
        if self.use_audio:
            try:
                print("🎵 Loading Whisper model...")
                self.whisper_model = whisper.load_model("turbo")
                print("✅ Whisper model loaded successfully")
            except Exception as e:
                print(f"❌ Failed to load Whisper model: {e}")
                print("🔄 Disabling audio analysis")
                self.use_audio = False
                self.whisper_model = None
        else:
            self.whisper_model = None
        
        # Test LLM connection
        self._test_llm_connection()
    
    def _test_llm_connection(self):
        """Test if LLM is working and model is available"""
        try:
            # Test the custom function
            test_request = {"content": "Say 'OK' if you can hear me"}
            test_response = get_response_content(test_request, [], self.model_name)
            
            if test_response:
                print(f"✅ LLM connected. Test response: {test_response[:50]}...")
            else:
                print("⚠️  LLM responded but with empty content")
            
        except Exception as e:
            print(f"❌ LLM connection failed: {e}")
            print("💡 Make sure ollama is installed and running")
            raise
    
    def get_video_files(self) -> List[Path]:
        """Get all MP4 files in video directory"""
        video_files = list(self.video_dir.glob("*.mp4"))
        print(f"📹 Found {len(video_files)} video files")
        return video_files
    
    def get_description(self, video_path: Path) -> str:
        """Get text description for a video file"""
        # Try multiple naming conventions
        possible_desc_files = [
            self.descriptions_dir / f"{video_path.stem}.txt",
            self.descriptions_dir / f"{video_path.name}.txt",
            self.descriptions_dir / f"{video_path.name.replace('.mp4', '.txt')}",
            self.descriptions_dir / f"{video_path.name.replace('.MP4', '.txt')}"
        ]
        
        for desc_file in possible_desc_files:
            if desc_file.exists():
                try:
                    content = desc_file.read_text(encoding='utf-8').strip()
                    if content:  # Make sure it's not empty
                        return content
                except Exception as e:
                    print(f"⚠️  Error reading {desc_file}: {e}")
                    continue
        
        # Fallback description
        return f"Video file: {video_path.name} (no description available)"
    
    def extract_and_transcribe_audio(self, video_path: Path) -> str:
        """Extract and transcribe audio from video"""
        if not self.use_audio or not self.whisper_model:
            return ""
        
        try:
            print(f"  🎵 Extracting audio from {video_path.name}...")
            transcript = extract_audio_from_vid(str(video_path))
            
            if transcript and transcript.strip():
                print(f"  ✅ Audio transcribed: {len(transcript)} characters")
                return transcript.strip()
            else:
                print(f"  ⚠️  No audio content transcribed")
                return ""
                
        except Exception as e:
            print(f"  ❌ Audio extraction failed: {e}")
            return ""
    
    def call_llm(self, prompt: str, frames: List = None) -> str:
        """Call LLM using the custom function"""
        if frames is None:
            frames = []
        
        request = {"content": prompt}
        response = get_response_content(request, frames, self.model_name)
        return response.strip() if response else "0"
    
    def score_video_multimodal(self, description: str, audio_transcript: str, 
                              user_prompt: str, frames: List[str] = None) -> float:
        """Score video using text description, audio transcript, and optional frames"""
        
        # Build comprehensive content description
        content_parts = []
        
        if description:
            content_parts.append(f"Text Description: {description}")
        
        if audio_transcript:
            content_parts.append(f"Audio Transcript: {audio_transcript}")
        
        if not content_parts:
            return 0.0
        
        combined_content = "\n\n".join(content_parts)
        
        # Enhanced scoring prompt that considers all modalities
        scoring_prompt = f"""You are a video search expert. Rate how well this video content matches the user's search request.

USER SEARCH: "{user_prompt}"

VIDEO CONTENT:
{combined_content}

{"You can also see visual frames from this video." if frames else ""}

Rate the match from 0-100 considering ALL available information:
- Text description relevance to search
- Audio content relevance to search (spoken words, dialogue, sounds)
{"- Visual content relevance to search" if frames else ""}
- Overall semantic match with user intent

Scoring scale:
- 100 = Perfect match (content exactly matches user request)
- 80-99 = Very good match (highly relevant across modalities)
- 60-79 = Good match (relevant in some aspects)
- 40-59 = Moderate match (partially relevant)
- 20-39 = Poor match (barely relevant)
- 0-19 = No match (completely irrelevant)

Consider:
- Semantic similarity (not just keyword matching)
- Context and intent of the user's request
- Quality and relevance of available information
- Multi-modal alignment (do audio, visual, text tell the same story?)

Respond with ONLY the number (0-100). No explanation.

SCORE:"""
        
        score_text = self.call_llm(scoring_prompt, frames if frames else [])
        
        # Extract number from response
        try:
            numbers = re.findall(r'\d+', score_text)
            if numbers:
                score = int(numbers[0])
                score = max(0, min(100, score))
                return score / 100.0
            else:
                print(f"⚠️  No number found in multimodal score: '{score_text}'")
                return 0.0
        except Exception as e:
            print(f"⚠️  Error parsing multimodal score: {e}")
            return 0.0
    
    def score_video_description(self, description: str, user_prompt: str) -> float:
        """Score how well a video description matches the user prompt (text only)"""
        
        scoring_prompt = f"""You are a video search expert. Rate how well this video description matches the user's search request.

USER SEARCH: "{user_prompt}"

VIDEO DESCRIPTION: "{description}"

Rate the match from 0-100 where:
- 100 = Perfect match, exactly what user wants  
- 80-99 = Very good match, highly relevant
- 60-79 = Good match, somewhat relevant
- 40-59 = Moderate match, partially relevant  
- 20-39 = Poor match, barely relevant
- 0-19 = No match, completely irrelevant

Consider semantic meaning, not just keywords. A video about "ocean waves" matches "sea footage" even without exact words.

Respond with ONLY the number (0-100). No explanation needed.

SCORE:"""
        
        score_text = self.call_llm(scoring_prompt)
        
        # Extract number from response - be very robust
        try:
            # Look for any number in the response
            numbers = re.findall(r'\d+', score_text)
            if numbers:
                score = int(numbers[0])
                # Clamp to valid range
                score = max(0, min(100, score))
                return score / 100.0  # Normalize to 0-1
            else:
                print(f"⚠️  No number found in score response: '{score_text}'")
                return 0.0
        except Exception as e:
            print(f"⚠️  Error parsing score '{score_text}': {e}")
            return 0.0
    
    def extract_desc_and_frames(self, video_path: Path, original_description: str) -> Tuple[str, List[str]]:
        """Enhance description using extracted frames and LLM vision"""
        try:
            print(f"  🖼️  Extracting frames for {video_path.name}...")
            frames = extract_frames_from_vid(str(video_path))
            
            if not frames or len(frames) == 0:
                print(f"  ⚠️  No frames extracted")
                return original_description, []
            
            print(f"  ✅ Extracted {len(frames)} frames")
            
            # # Analyze frames with LLM vision
            # frame_analysis = self._analyze_frames_with_vision(original_description, frames[:3])
            
            # if frame_analysis and frame_analysis.strip():
            #     enhanced = f"{original_description}\n\nVisual analysis: {frame_analysis}"
            #     return enhanced, frames
            # else:
            return original_description, frames
                
        except Exception as e:
            print(f"  ❌ Error enhancing description: {e}")
            return original_description, []
    
    def _analyze_frames_with_vision(self, description: str, frames: List[str]) -> str:
        """Analyze frames using LLM vision capabilities"""
        try:
            analysis_prompt = f"""Look at these video frames and enhance the existing description.

EXISTING DESCRIPTION: "{description}"

Analyze the visual content in these {len(frames)} frames and provide additional details about:
- Visual style and mood
- Colors and lighting
- Actions or movement visible
- Setting and environment
- People and their expressions/emotions
- Objects and their context
- Any notable visual elements

Keep your analysis brief (2-3 sentences) and focus on details that would help match this video to search queries.

VISUAL ANALYSIS:"""
            
            return self.call_llm(analysis_prompt, frames)
                
        except Exception as e:
            print(f"    ⚠️  Error in visual frame analysis: {e}")
            return ""
    
    def enhance_description_with_audio(self, video_path: Path, original_description: str, 
                                     audio_transcript: str) -> str:
        """Enhance description by analyzing audio transcript"""
        if not audio_transcript or not audio_transcript.strip():
            return original_description
        
        try:
            enhancement_prompt = f"""Analyze this video's audio transcript to enhance the existing description.

EXISTING DESCRIPTION: "{original_description}"

AUDIO TRANSCRIPT: "{audio_transcript}"

Based on the spoken content, enhance the description by adding relevant details about:
- What people are saying or discussing
- Emotional tone of speech (happy, sad, excited, etc.)
- Language(s) spoken
- Key topics or subjects mentioned
- Any music, sounds, or ambient audio
- Context that helps understand the video content

Provide 1-2 sentences that would help match this video to search queries. Focus on content that complements the visual description.

AUDIO ENHANCEMENT:"""
            
            audio_analysis = self.call_llm(enhancement_prompt)
            
            if audio_analysis and audio_analysis.strip():
                enhanced = f"{original_description}\n\nAudio content: {audio_analysis}"
                return enhanced
            else:
                return original_description
                
        except Exception as e:
            print(f"    ⚠️  Error in audio analysis: {e}")
            return original_description
    
    def find_best_match(self, user_prompt: str, use_frames: bool = False, 
                       use_audio: bool = None, top_k: int = 5) -> Tuple[str, Dict]:
        """
        Find the video that best matches the user prompt
        
        Args:
            user_prompt: User's search query
            use_frames: Whether to enhance descriptions with frame analysis  
            use_audio: Whether to use audio analysis (None = use class default)
            top_k: Number of top candidates to return
            
        Returns:
            Tuple of (best_video_name, complete_results_dict)
        """
        print(f"\n🔍 Searching for: '{user_prompt}'")
        print("=" * 50)
        
        # Determine if audio should be used
        if use_audio is None:
            use_audio = self.use_audio
        elif use_audio and not self.use_audio:
            print("⚠️  Audio requested but not initialized, disabling audio analysis")
            use_audio = False
        
        video_files = self.get_video_files()
        
        if not video_files:
            raise ValueError("❌ No MP4 files found in video directory")
        
        candidates = []
        
        for i, video_path in enumerate(video_files):
            print(f"📹 Processing {i+1}/{len(video_files)}: {video_path.name}")
            
            # Get basic description
            description = self.get_description(video_path)
            print(f"  📝 Description: {description[:100]}...")
            
            # Extract audio transcript if enabled
            audio_transcript = ""
            if use_audio:
                audio_transcript = self.extract_and_transcribe_audio(video_path)
            
            # Process based on enabled modalities
            if use_frames and use_audio:
                # Full multimodal analysis
                print(f"  🎬 Full multimodal analysis...")
                
                enhanced_description, frames = self.extract_desc_and_frames(video_path, description)
                if audio_transcript:
                    enhanced_description = self.enhance_description_with_audio(
                        video_path, enhanced_description, audio_transcript
                    )
                
                score = self.score_video_multimodal(
                    enhanced_description, audio_transcript, user_prompt, frames
                )
                final_description = enhanced_description
                
            elif use_frames:
                # Visual + text analysis
                print(f"  🖼️  Visual analysis...")
                enhanced_description, frames = self.extract_desc_and_frames(video_path, description)
                score = self.score_video_multimodal(
                    enhanced_description, "", user_prompt, frames
                )
                final_description = enhanced_description
                
            elif use_audio:
                # Audio + text analysis  
                print(f"  🎵 Audio analysis...")
                if audio_transcript:
                    enhanced_description = self.enhance_description_with_audio(
                        video_path, description, audio_transcript
                    )
                else:
                    enhanced_description = description
                score = self.score_video_multimodal(
                    enhanced_description, audio_transcript, user_prompt
                )
                final_description = enhanced_description
                
            else:
                # Text-only analysis
                print(f"  🤖 Text-only scoring...")
                score = self.score_video_description(description, user_prompt)
                final_description = description
            
            print(f"  📊 Score: {score:.3f}")
            
            # Create candidate
            candidate = VideoCandidate(
                filename=video_path.name,
                path=str(video_path),
                description=final_description,
                audio_transcript=audio_transcript,
                score=score,
                file_size=video_path.stat().st_size
            )
            candidates.append(candidate)
            print(f"  ✅ Done\n")
        
        # Sort by score (highest first)
        candidates.sort(key=lambda x: x.score, reverse=True)
        
        if not candidates:
            raise ValueError("❌ No suitable videos found")
        
        best_candidate = candidates[0]
        
        # Prepare complete results
        results = {
            "best_match": {
                "filename": best_candidate.filename,
                "path": best_candidate.path,
                "description": best_candidate.description,
                "audio_transcript": best_candidate.audio_transcript,
                "score": best_candidate.score,
                "file_size": best_candidate.file_size
            },
            "candidate_ranking": [
                {
                    "rank": i + 1,
                    "filename": candidate.filename,
                    "score": candidate.score,
                    "description": candidate.description,
                    "audio_transcript": candidate.audio_transcript,
                    "path": candidate.path
                }
                for i, candidate in enumerate(candidates)
            ],
            "search_summary": {
                "query": user_prompt,
                "total_videos_analyzed": len(video_files),
                "best_score": best_candidate.score,
                "used_frame_analysis": use_frames,
                "used_audio_analysis": use_audio,
                "modalities_used": self._get_modalities_used(use_frames, use_audio)
            }
        }
        
        # Print results
        print("🏆 RESULTS")
        print("=" * 50)
        print(f"🥇 Best match: {best_candidate.filename}")
        print(f"📊 Score: {best_candidate.score:.3f}")
        print(f"📝 Description: {best_candidate.description[:150]}...")
        if best_candidate.audio_transcript:
            print(f"🎵 Audio: {best_candidate.audio_transcript[:100]}...")
        print(f"\n📋 Top {min(top_k, len(candidates))} candidates:")
        
        for i, candidate in enumerate(candidates[:top_k]):
            emoji = ["🥇", "🥈", "🥉", "4️⃣", "5️⃣"][i] if i < 5 else f"{i+1}️⃣"
            audio_indicator = " 🎵" if candidate.audio_transcript else ""
            print(f"  {emoji} {candidate.filename} (score: {candidate.score:.3f}){audio_indicator}")
        
        return best_candidate.filename, results
    
    def _get_modalities_used(self, use_frames: bool, use_audio: bool) -> List[str]:
        """Get list of modalities used in analysis"""
        modalities = ["text"]
        if use_frames:
            modalities.append("visual")
        if use_audio:
            modalities.append("audio")
        return modalities


# Main API function with audio support
def find_best_video(prompt: str, video_dir: str = "videos", descriptions_dir: str = "descriptions", 
                   use_frames: bool = False, use_audio: bool = False, 
                   model_name: str = "gemma3:14b", top_k: int = 5) -> Tuple[str, Dict]:
    """
    🎯 Main API function - find best matching video with multimodal analysis
    
    Args:
        prompt: What you're looking for ("ocean waves", "celebration", etc.)
        video_dir: Folder with your MP4 files
        descriptions_dir: Folder with your TXT description files
        use_frames: Whether to analyze visual frames for better matching
        use_audio: Whether to analyze audio/speech content for better matching
        model_name: LLM model to use (gemma3:14b, gemma3:12b, etc.)
        top_k: Number of top results to return
        
    Returns:
        (best_video_filename, complete_results_dict)
    """
    matcher = VideoMatcher(video_dir, descriptions_dir, model_name, use_audio=use_audio)
    return matcher.find_best_match(prompt, use_frames, use_audio, top_k=top_k)


# Enhanced demo with audio
def demo():
    """Run a demo with multimodal analysis"""
    print("🚀 MULTIMODAL VIDEO MATCHER DEMO")
    print("=" * 60)
    
    # Test with sample prompts
    test_prompts = [
        # "show your favorite local landmark",
        # "people speaking in German", 
        # "joyful celebration",
        # "women in gym clothes",
        "replace the video with a south-east asian man"
    ]
    
    for prompt in test_prompts:
        try:
            print(f"\n🎬 TESTING: '{prompt}'")
            print("-" * 40)
            
            video_name, results = find_best_video(
                prompt=prompt,
                video_dir="./testvids",
                descriptions_dir="./testdescs",
                use_frames=True,    # Visual analysis
                use_audio=True,     # Audio analysis  
                model_name="gemma3:12b",
                top_k=5
            )
            
            print(f"✅ SUCCESS: Found {video_name}")
            
        except Exception as e:
            print(f"❌ ERROR: {e}")


# Quick test function with audio
def quick_test_multimodal(prompt: str = "someone being very joyful"):
    """Quick test with multimodal analysis"""
    try:
        print(f"🔍 Multimodal search for: '{prompt}'")
        print("🎬 Using: Text + Visual + Audio analysis")
        
        video_name, results = find_best_video(
            prompt=prompt,
            video_dir="./testvids",
            descriptions_dir="./testdescs", 
            use_frames=True,
            use_audio=True,
            model_name="gemma3:12b",
            top_k=10
        )

        print(f"\n🏆 BEST MATCH:")
        print(f"📹 Video: {video_name}")
        print(f"📊 Score: {results['best_match']['score']:.3f}")
        print(f"📝 Description: {results['best_match']['description'][:200]}...")
        
        if results['best_match']['audio_transcript']:
            print(f"🎵 Audio: {results['best_match']['audio_transcript'][:150]}...")
        
        print(f"\n🔧 Analysis used: {', '.join(results['search_summary']['modalities_used'])}")
        
        return video_name, results
        
    except Exception as e:
        print(f"❌ Error in multimodal test: {e}")
        return None, None


# if __name__ == "__main__":
    # Test multimodal search
    # try:
    #     print(f"🔍 Multimodal test: 'Donne moi une vidéo où l'on voit quelqu'un être très joyeux.'")
    #     video_name, results = find_best_video(
    #         prompt="Donne moi une vidéo où l'on voit quelqu'un être très joyeux.",
    #         video_dir="./testvids",
    #         descriptions_dir="./testdescs",
    #         use_frames=True,
    #         use_audio=True,  # Enable audio analysis
    #         model_name="gemma3:12b",
    #         top_k=10
    #     )

    #     print(f"Best match: {video_name}")
    #     print(f"Score: {results['best_match']['score']:.3f}")
    #     print(f"Description: {results['best_match']['description'][:200]}...")
    #     if results['best_match']['audio_transcript']:
    #         print(f"Audio content: {results['best_match']['audio_transcript'][:150]}...")
    #     print(results["candidate_ranking"])
        
    # except Exception as e:
    #     print(f"❌ Error in test: {e}")
#%%

demo()
#%%
# Video Matcher with Audio Analysis - Enhanced Edition
# Includes visual frames + audio transcription analysis

import os
import ollama
from pathlib import Path
from typing import List, Dict, Tuple, Optional
import cv2
import re
from dataclasses import dataclass
import time
import base64
import whisper
from df.enhance import enhance, init_df, load_audio, save_audio
from pydub import AudioSegment
import torchaudio
import cv2
import ffmpeg as ff
import VideoAnalysisAI as VAI

@dataclass
class VideoCandidate:
    """Data class for video candidates"""
    filename: str
    path: str
    description: str
    audio_transcript: str
    score: float
    file_size: int

class VideoMatcher:
    def __init__(self, video_dir: str, descriptions_dir: str, model_name: str = "gemma3:14b", 
                 use_audio: bool = False):
        """
        Initialize VideoMatcher with optional audio analysis
        
        Args:
            video_dir: Directory containing MP4 files
            descriptions_dir: Directory containing TXT description files  
            model_name: LLM model name (gemma3:14b, gemma3:12b, etc.)
            use_audio: Whether to enable audio transcription analysis
        """
        self.video_dir = Path(video_dir)
        self.descriptions_dir = Path(descriptions_dir)
        self.model_name = model_name
        self.use_audio = use_audio
        
        print(f"🎬 VideoMatcher initialized")
        print(f"📁 Video directory: {self.video_dir}")
        print(f"📄 Descriptions directory: {self.descriptions_dir}")
        print(f"🤖 Using model: {self.model_name}")
        print(f"🎵 Audio analysis: {'Enabled' if use_audio else 'Disabled'}")
        
        # Initialize Whisper model if audio analysis is enabled
        if self.use_audio:
            try:
                print("🎵 Loading Whisper model...")
                self.whisper_model = whisper.load_model("turbo")
                print("✅ Whisper model loaded successfully")
            except Exception as e:
                print(f"❌ Failed to load Whisper model: {e}")
                print("🔄 Disabling audio analysis")
                self.use_audio = False
                self.whisper_model = None
        else:
            self.whisper_model = None
        
        # Test LLM connection
        self._test_llm_connection()
    
    def _test_llm_connection(self):
        """Test if LLM is working and model is available"""
        try:
            # Test the custom function
            test_request = {"content": "Say 'OK' if you can hear me"}
            test_response = VAI.get_response_content(test_request, [], self.model_name)
            
            if test_response:
                print(f"✅ LLM connected. Test response: {test_response[:50]}...")
            else:
                print("⚠️  LLM responded but with empty content")
            
        except Exception as e:
            print(f"❌ LLM connection failed: {e}")
            print("💡 Make sure ollama is installed and running")
            raise
    
    def get_video_files(self) -> List[Path]:
        """Get all MP4 files in video directory"""
        video_files = list(self.video_dir.glob("*.mp4"))
        print(f"📹 Found {len(video_files)} video files")
        return video_files
    
    def get_description(self, video_path: Path) -> str:
        """Get text description for a video file"""
        # Try multiple naming conventions
        possible_desc_files = [
            self.descriptions_dir / f"{video_path.stem}.txt",
            self.descriptions_dir / f"{video_path.name}.txt",
            self.descriptions_dir / f"{video_path.name.replace('.mp4', '.txt')}",
            self.descriptions_dir / f"{video_path.name.replace('.MP4', '.txt')}"
        ]
        
        for desc_file in possible_desc_files:
            if desc_file.exists():
                try:
                    content = desc_file.read_text(encoding='utf-8').strip()
                    if content:  # Make sure it's not empty
                        return content
                except Exception as e:
                    print(f"⚠️  Error reading {desc_file}: {e}")
                    continue
        
        # Fallback description
        return f"Video file: {video_path.name} (no description available)"
    
    def extract_and_transcribe_audio(self, video_path: Path) -> str:
        """Extract and transcribe audio from video"""
        if not self.use_audio or not self.whisper_model:
            return ""
        
        try:
            print(f"  🎵 Extracting audio from {video_path.name}...")
            transcript = VAI.extract_audio_from_vid(str(video_path))
            
            if transcript and transcript.strip():
                print(f"  ✅ Audio transcribed: {len(transcript)} characters")
                return transcript.strip()
            else:
                print(f"  ⚠️  No audio content transcribed")
                return ""
                
        except Exception as e:
            print(f"  ❌ Audio extraction failed: {e}")
            return ""
    
    def call_llm(self, prompt: str, frames: List = None) -> str:
        """Call LLM using the custom function"""
        if frames is None:
            frames = []
        
        request = {"content": prompt}
        response = VAI.get_response_content(request, frames, self.model_name)
        return response.strip() if response else "0"
    
    def score_video_multimodal(self, description: str, audio_transcript: str, 
                              user_prompt: str, frames: List[str] = None) -> float:
        """Score video using text description, audio transcript, and optional frames"""
        
        # Build comprehensive content description
        content_parts = []
        
        if description:
            content_parts.append(f"Text Description: {description}")
        
        if audio_transcript:
            content_parts.append(f"Audio Transcript: {audio_transcript}")
        
        if not content_parts:
            return 0.0
        
        combined_content = "\n\n".join(content_parts)
        
        # Enhanced scoring prompt that considers all modalities
        scoring_prompt = f"""You are a video search expert. Rate how well this video content matches the user's search request.

USER SEARCH: "{user_prompt}"

VIDEO CONTENT:
{combined_content}

{"You can also see visual frames from this video." if frames else ""}

Rate the match from 0-100 considering ALL available information:
- Text description relevance to search
- Audio content relevance to search (spoken words, dialogue, sounds)
{"- Visual content relevance to search" if frames else ""}
- Overall semantic match with user intent

Scoring scale:
- 100 = Perfect match (content exactly matches user request)
- 80-99 = Very good match (highly relevant across modalities)
- 60-79 = Good match (relevant in some aspects)
- 40-59 = Moderate match (partially relevant)
- 20-39 = Poor match (barely relevant)
- 0-19 = No match (completely irrelevant)

Consider:
- Semantic similarity (not just keyword matching)
- Context and intent of the user's request
- Quality and relevance of available information
- Multi-modal alignment (do audio, visual, text tell the same story?)

Respond with ONLY the number (0-100). No explanation.

SCORE:"""
        
        score_text = self.call_llm(scoring_prompt, frames if frames else [])
        
        # Extract number from response
        try:
            numbers = re.findall(r'\d+', score_text)
            if numbers:
                score = int(numbers[0])
                score = max(0, min(100, score))
                return score / 100.0
            else:
                print(f"⚠️  No number found in multimodal score: '{score_text}'")
                return 0.0
        except Exception as e:
            print(f"⚠️  Error parsing multimodal score: {e}")
            return 0.0
    
    def score_video_description(self, description: str, user_prompt: str) -> float:
        """Score how well a video description matches the user prompt (text only)"""
        
        scoring_prompt = f"""You are a video search expert. Rate how well this video description matches the user's search request.

USER SEARCH: "{user_prompt}"

VIDEO DESCRIPTION: "{description}"

Rate the match from 0-100 where:
- 100 = Perfect match, exactly what user wants  
- 80-99 = Very good match, highly relevant
- 60-79 = Good match, somewhat relevant
- 40-59 = Moderate match, partially relevant  
- 20-39 = Poor match, barely relevant
- 0-19 = No match, completely irrelevant

Consider semantic meaning, not just keywords. A video about "ocean waves" matches "sea footage" even without exact words.

Respond with ONLY the number (0-100). No explanation needed.

SCORE:"""
        
        score_text = self.call_llm(scoring_prompt)
        
        # Extract number from response - be very robust
        try:
            # Look for any number in the response
            numbers = re.findall(r'\d+', score_text)
            if numbers:
                score = int(numbers[0])
                # Clamp to valid range
                score = max(0, min(100, score))
                return score / 100.0  # Normalize to 0-1
            else:
                print(f"⚠️  No number found in score response: '{score_text}'")
                return 0.0
        except Exception as e:
            print(f"⚠️  Error parsing score '{score_text}': {e}")
            return 0.0
    
    def extract_desc_and_frames(self, video_path: Path, original_description: str) -> Tuple[str, List[str]]:
        """Enhance description using extracted frames and LLM vision"""
        try:
            print(f"  🖼️  Extracting frames for {video_path.name}...")
            frames = VAI.extract_frames_from_vid(str(video_path))
            
            if not frames or len(frames) == 0:
                print(f"  ⚠️  No frames extracted")
                return original_description, []
            
            print(f"  ✅ Extracted {len(frames)} frames")
            
            # # Analyze frames with LLM vision
            # frame_analysis = self._analyze_frames_with_vision(original_description, frames[:3])
            
            # if frame_analysis and frame_analysis.strip():
            #     enhanced = f"{original_description}\n\nVisual analysis: {frame_analysis}"
            #     return enhanced, frames
            # else:
            return original_description, frames
                
        except Exception as e:
            print(f"  ❌ Error enhancing description: {e}")
            return original_description, []
    
    def _analyze_frames_with_vision(self, description: str, frames: List[str]) -> str:
        """Analyze frames using LLM vision capabilities"""
        try:
            analysis_prompt = f"""Look at these video frames and enhance the existing description.

EXISTING DESCRIPTION: "{description}"

Analyze the visual content in these {len(frames)} frames and provide additional details about:
- Visual style and mood
- Colors and lighting
- Actions or movement visible
- Setting and environment
- People and their expressions/emotions
- Objects and their context
- Any notable visual elements

Keep your analysis brief (2-3 sentences) and focus on details that would help match this video to search queries.

VISUAL ANALYSIS:"""
            
            return self.call_llm(analysis_prompt, frames)
                
        except Exception as e:
            print(f"    ⚠️  Error in visual frame analysis: {e}")
            return ""
    
    def enhance_description_with_audio(self, video_path: Path, original_description: str, 
                                     audio_transcript: str) -> str:
        """Enhance description by analyzing audio transcript"""
        if not audio_transcript or not audio_transcript.strip():
            return original_description
        
        try:
            enhancement_prompt = f"""Analyze this video's audio transcript to enhance the existing description.

EXISTING DESCRIPTION: "{original_description}"

AUDIO TRANSCRIPT: "{audio_transcript}"

Based on the spoken content, enhance the description by adding relevant details about:
- What people are saying or discussing
- Emotional tone of speech (happy, sad, excited, etc.)
- Language(s) spoken
- Key topics or subjects mentioned
- Any music, sounds, or ambient audio
- Context that helps understand the video content

Provide 1-2 sentences that would help match this video to search queries. Focus on content that complements the visual description.

AUDIO ENHANCEMENT:"""
            
            audio_analysis = self.call_llm(enhancement_prompt)
            
            if audio_analysis and audio_analysis.strip():
                enhanced = f"{original_description}\n\nAudio content: {audio_analysis}"
                return enhanced
            else:
                return original_description
                
        except Exception as e:
            print(f"    ⚠️  Error in audio analysis: {e}")
            return original_description
    
    def find_best_match(self, user_prompt: str, use_frames: bool = False, 
                       use_audio: bool = None, top_k: int = 5) -> Tuple[str, Dict]:
        """
        Find the video that best matches the user prompt
        
        Args:
            user_prompt: User's search query
            use_frames: Whether to enhance descriptions with frame analysis  
            use_audio: Whether to use audio analysis (None = use class default)
            top_k: Number of top candidates to return
            
        Returns:
            Tuple of (best_video_name, complete_results_dict)
        """
        print(f"\n🔍 Searching for: '{user_prompt}'")
        print("=" * 50)
        
        # Determine if audio should be used
        if use_audio is None:
            use_audio = self.use_audio
        elif use_audio and not self.use_audio:
            print("⚠️  Audio requested but not initialized, disabling audio analysis")
            use_audio = False
        
        video_files = self.get_video_files()
        
        if not video_files:
            raise ValueError("❌ No MP4 files found in video directory")
        
        candidates = []
        
        for i, video_path in enumerate(video_files):
            print(f"📹 Processing {i+1}/{len(video_files)}: {video_path.name}")
            
            # Get basic description
            description = self.get_description(video_path)
            print(f"  📝 Description: {description[:100]}...")
            
            # Extract audio transcript if enabled
            audio_transcript = ""
            if use_audio:
                audio_transcript = self.extract_and_transcribe_audio(video_path)
            
            # Process based on enabled modalities
            if use_frames and use_audio:
                # Full multimodal analysis
                print(f"  🎬 Full multimodal analysis...")
                
                enhanced_description, frames = self.extract_desc_and_frames(video_path, description)
                if audio_transcript:
                    enhanced_description = self.enhance_description_with_audio(
                        video_path, enhanced_description, audio_transcript
                    )
                
                score = self.score_video_multimodal(
                    enhanced_description, audio_transcript, user_prompt, frames
                )
                final_description = enhanced_description
                
            elif use_frames:
                # Visual + text analysis
                print(f"  🖼️  Visual analysis...")
                enhanced_description, frames = self.extract_desc_and_frames(video_path, description)
                score = self.score_video_multimodal(
                    enhanced_description, "", user_prompt, frames
                )
                final_description = enhanced_description
                
            elif use_audio:
                # Audio + text analysis  
                print(f"  🎵 Audio analysis...")
                if audio_transcript:
                    enhanced_description = self.enhance_description_with_audio(
                        video_path, description, audio_transcript
                    )
                else:
                    enhanced_description = description
                score = self.score_video_multimodal(
                    enhanced_description, audio_transcript, user_prompt
                )
                final_description = enhanced_description
                
            else:
                # Text-only analysis
                print(f"  🤖 Text-only scoring...")
                score = self.score_video_description(description, user_prompt)
                final_description = description
            
            print(f"  📊 Score: {score:.3f}")
            
            # Create candidate
            candidate = VideoCandidate(
                filename=video_path.name,
                path=str(video_path),
                description=final_description,
                audio_transcript=audio_transcript,
                score=score,
                file_size=video_path.stat().st_size
            )
            candidates.append(candidate)
            print(f"  ✅ Done\n")
        
        # Sort by score (highest first)
        candidates.sort(key=lambda x: x.score, reverse=True)
        
        if not candidates:
            raise ValueError("❌ No suitable videos found")
        
        best_candidate = candidates[0]
        
        # Prepare complete results
        results = {
            "best_match": {
                "filename": best_candidate.filename,
                "path": best_candidate.path,
                "description": best_candidate.description,
                "audio_transcript": best_candidate.audio_transcript,
                "score": best_candidate.score,
                "file_size": best_candidate.file_size
            },
            "candidate_ranking": [
                {
                    "rank": i + 1,
                    "filename": candidate.filename,
                    "score": candidate.score,
                    "description": candidate.description,
                    "audio_transcript": candidate.audio_transcript,
                    "path": candidate.path
                }
                for i, candidate in enumerate(candidates)
            ],
            "search_summary": {
                "query": user_prompt,
                "total_videos_analyzed": len(video_files),
                "best_score": best_candidate.score,
                "used_frame_analysis": use_frames,
                "used_audio_analysis": use_audio,
                "modalities_used": self._get_modalities_used(use_frames, use_audio)
            }
        }
        
        # Print results
        print("🏆 RESULTS")
        print("=" * 50)
        print(f"🥇 Best match: {best_candidate.filename}")
        print(f"📊 Score: {best_candidate.score:.3f}")
        print(f"📝 Description: {best_candidate.description[:150]}...")
        if best_candidate.audio_transcript:
            print(f"🎵 Audio: {best_candidate.audio_transcript[:100]}...")
        print(f"\n📋 Top {min(top_k, len(candidates))} candidates:")
        
        for i, candidate in enumerate(candidates[:top_k]):
            emoji = ["🥇", "🥈", "🥉", "4️⃣", "5️⃣"][i] if i < 5 else f"{i+1}️⃣"
            audio_indicator = " 🎵" if candidate.audio_transcript else ""
            print(f"  {emoji} {candidate.filename} (score: {candidate.score:.3f}){audio_indicator}")
        
        return best_candidate.filename, results
    
    def _get_modalities_used(self, use_frames: bool, use_audio: bool) -> List[str]:
        """Get list of modalities used in analysis"""
        modalities = ["text"]
        if use_frames:
            modalities.append("visual")
        if use_audio:
            modalities.append("audio")
        return modalities


# Main API function with audio support
def find_best_video(prompt: str, video_dir: str = "videos", descriptions_dir: str = "descriptions", 
                   use_frames: bool = False, use_audio: bool = False, 
                   model_name: str = "gemma3:14b", top_k: int = 5) -> Tuple[str, Dict]:
    """
    🎯 Main API function - find best matching video with multimodal analysis
    
    Args:
        prompt: What you're looking for ("ocean waves", "celebration", etc.)
        video_dir: Folder with your MP4 files
        descriptions_dir: Folder with your TXT description files
        use_frames: Whether to analyze visual frames for better matching
        use_audio: Whether to analyze audio/speech content for better matching
        model_name: LLM model to use (gemma3:14b, gemma3:12b, etc.)
        top_k: Number of top results to return
        
    Returns:
        (best_video_filename, complete_results_dict)
    """
    matcher = VideoMatcher(video_dir, descriptions_dir, model_name, use_audio=use_audio)
    return matcher.find_best_match(prompt, use_frames, use_audio, top_k=top_k)


# Enhanced demo with audio
def demo():
    """Run a demo with multimodal analysis"""
    print("🚀 MULTIMODAL VIDEO MATCHER DEMO")
    print("=" * 60)
    
    # Test with sample prompts
    test_prompts = [
        "show your favorite local landmark",
        "people speaking in German", 
        "joyful celebration",
        "women in gym clothes",
        "replace the video with a south-east asian man"
    ]
    
    for prompt in test_prompts:
        try:
            print(f"\n🎬 TESTING: '{prompt}'")
            print("-" * 40)
            
            video_name, results = find_best_video(
                prompt=prompt,
                video_dir="./testvids",
                descriptions_dir="./testdescs",
                use_frames=True,    # Visual analysis
                use_audio=True,     # Audio analysis  
                model_name="gemma3:12b",
                top_k=5
            )
            
            print(f"✅ SUCCESS: Found {video_name}")
            
        except Exception as e:
            print(f"❌ ERROR: {e}")


# Quick test function with audio
def quick_test_multimodal(prompt: str = "someone being very joyful"):
    """Quick test with multimodal analysis"""
    try:
        print(f"🔍 Multimodal search for: '{prompt}'")
        print("🎬 Using: Text + Visual + Audio analysis")
        
        video_name, results = find_best_video(
            prompt=prompt,
            video_dir="./testvids",
            descriptions_dir="./testdescs", 
            use_frames=True,
            use_audio=True,
            model_name="gemma3:12b",
            top_k=10
        )

        print(f"\n🏆 BEST MATCH:")
        print(f"📹 Video: {video_name}")
        print(f"📊 Score: {results['best_match']['score']:.3f}")
        print(f"📝 Description: {results['best_match']['description'][:200]}...")
        
        if results['best_match']['audio_transcript']:
            print(f"🎵 Audio: {results['best_match']['audio_transcript'][:150]}...")
        
        print(f"\n🔧 Analysis used: {', '.join(results['search_summary']['modalities_used'])}")
        
        return video_name, results
        
    except Exception as e:
        print(f"❌ Error in multimodal test: {e}")
        return None, None
#%%
demo()
#%%
def check_data_access_requirements(user_query: str, model_name: str = "gemma3:12b"):
   """
   Check if user query requires access to user data or video metadata
   
   Args:
       user_query: The user's search query
       model_name: LLM model to use for analysis
       
   Returns:
       Dict containing access requirements and reasoning
   """
   import json
   
   analysis_prompt = f"""Analyze this user query to determine what data access is required.

USER QUERY: "{user_query}"

Determine if this query requires access to:

1. USER DATA - Personal information like:
  - Friends list, contacts, social connections
  - User preferences, history, profile
  - Personal videos, private content
  - User's own uploads or creations
  
2. VIDEO METADATA - Information about videos like:
  - Video author/creator name
  - Upload date, view counts, ratings
  - Video tags, categories, ownership
  - Creator profiles or channel info

3. EXISTING FILTERING - Standard filtering options like:
  - Date-based filtering (recent, old, specific time periods)
  - Language filtering (videos in specific languages)
  - Geographical region filtering (videos from specific countries/regions)
  - Community filtering (videos from specific communities or groups)

Respond ONLY with valid JSON in this exact format:
{{
   "requires_user_data": true/false,
   "requires_video_metadata": true/false,
   "requires_existing_filtering": true/false,
   "user_data_types": ["friends", "preferences", "profile", "uploads"],
   "metadata_types": ["author", "date", "views", "tags", "ownership"],
   "filtering_types": ["date", "language", "geographical", "community"],
   "confidence": 0.95,
   "reasoning": "Brief explanation of why these access types are needed"
}}

Set empty arrays for types not needed. Set confidence 0-1 based on clarity of requirements.

JSON Response:"""

   try:
       request = {"content": analysis_prompt}
       response = get_response_content(request, [], model_name)
       
       # Extract JSON from response
       json_match = re.search(r'\{.*\}', response, re.DOTALL)
       if json_match:
           json_text = json_match.group(0)
           parsed_response = json.loads(json_text)
           
           # Validate and set defaults
           return {
               "requires_user_data": parsed_response.get("requires_user_data", False),
               "requires_video_metadata": parsed_response.get("requires_video_metadata", False), 
               "requires_existing_filtering": parsed_response.get("requires_existing_filtering", False),
               "user_data_types": parsed_response.get("user_data_types", []),
               "metadata_types": parsed_response.get("metadata_types", []),
               "filtering_types": parsed_response.get("filtering_types", []),
               "confidence": float(parsed_response.get("confidence", 0.0)),
               "reasoning": parsed_response.get("reasoning", ""),
               "query": user_query
           }
           
   except Exception as e:
       print(f"⚠️  Data access analysis failed: {e}")
       
   # Fallback response
   return {
       "requires_user_data": False,
       "requires_video_metadata": False,
       "requires_existing_filtering": False,
       "user_data_types": [],
       "metadata_types": [],
       "filtering_types": [],
       "confidence": 0.0,
       "reasoning": "Analysis failed, assuming no special access required",
       "query": user_query
   }

check_data_access_requirements("Give me clips which include my friend Jagmeet")