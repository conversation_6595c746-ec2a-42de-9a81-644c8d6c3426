# Use uv's Python 3.12 image as base
FROM ghcr.io/astral-sh/uv:python3.12-bookworm

# Set working directory
WORKDIR /app

# Install system dependencies for video/audio processing and Rust for DeepFilterLib
RUN apt-get update && apt-get install -y \
    ffmpeg \
    libsndfile1 \
    libsndfile1-dev \
    libportaudio2 \
    libportaudiocpp0 \
    portaudio19-dev \
    libasound2-dev \
    libpulse-dev \
    libgl1-mesa-glx \
    libglib2.0-0 \
    libsm6 \
    libxext6 \
    libxrender-dev \
    libgomp1 \
    curl \
    build-essential \
    && rm -rf /var/lib/apt/lists/*

# Install Rust for building DeepFilterLib
RUN curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh -s -- -y
ENV PATH="/root/.cargo/bin:${PATH}"

# Set environment variables for uv
ENV UV_SYSTEM_PYTHON=1
ENV UV_COMPILE_BYTECODE=1
ENV UV_LINK_MODE=copy

# Copy requirements first for better caching
COPY requirements.txt .

# Install Python dependencies with uv
# Handle different architectures for PyTorch installation
RUN --mount=type=cache,target=/root/.cache/uv \
    if [ "$(uname -m)" = "x86_64" ]; then \
        echo "Installing PyTorch for x86_64..."; \
        uv pip install --index-url https://download.pytorch.org/whl/cpu torch==2.6.0+cpu torchaudio==2.6.0+cpu; \
    else \
        echo "Installing PyTorch for ARM64..."; \
        uv pip install torch==2.6.0 torchaudio==2.6.0; \
    fi

# Install remaining dependencies (excluding torch and torchaudio since we installed them above)
RUN --mount=type=cache,target=/root/.cache/uv \
    sed '/^torch==/d; /^torchaudio/d' requirements.txt > requirements_no_torch.txt && \
    uv pip install -r requirements_no_torch.txt

# Copy the rest of the application
COPY . .

# Create necessary directories
RUN mkdir -p /app/tmp /app/cache /app/testvids /app/testdescs /app/demofiles

# Set permissions
RUN chmod +x /app/*.py

# Expose port if needed (adjust as necessary)
EXPOSE 8000

# Default command - you can override this when running the container
CMD ["python", "VideoAnalysisAI.py"]
