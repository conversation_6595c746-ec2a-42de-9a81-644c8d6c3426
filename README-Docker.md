# YIQQI AI Workflows - Docker Setup

This Docker setup solves the PyTorch compatibility issues on macOS by running the application in a Linux container with proper PyTorch CPU wheels.

## Quick Start

### Prerequisites
- Docker Desktop installed and running
- Docker Compose (included with Docker Desktop)

### Option 1: Using the Helper Script (Recommended)

```bash
# Build the Docker image
./docker-dev.sh build

# Start development environment (builds if needed and opens shell)
./docker-dev.sh dev

# Run a specific script
./docker-dev.sh run VideoAnalysisAI.py

# Start Jupyter notebook server
./docker-dev.sh jupyter
```

### Option 2: Using Docker Compose Directly

```bash
# Build the image
docker-compose build

# Start development environment
docker-compose up -d
docker-compose exec yiqqi-ai-workflows /bin/bash

# Run a specific script
docker-compose run --rm yiqqi-ai-workflows python VideoAnalysisAI.py

# Stop the environment
docker-compose down
```

## Development Workflow

### 1. Initial Setup
```bash
# Clone the repository (if not already done)
# cd into the project directory

# Build the Docker image
./docker-dev.sh build
```

### 2. Development
```bash
# Start development environment
./docker-dev.sh dev

# Inside the container, you can run any Python script:
python VideoAnalysisAI.py
python VideoClipSelectionAssistantAI.py

# Or run Jupyter notebooks:
jupyter notebook --ip=0.0.0.0 --port=8888 --no-browser --allow-root
```

### 3. Installing New Dependencies
```bash
# Install a new package
./docker-dev.sh install package_name

# Update requirements.txt
./docker-dev.sh freeze
```

### 4. File Synchronization
- Your local files are automatically synced to the container
- Changes made locally are immediately available in the container
- The `.venv` directory is excluded to prevent conflicts

## Available Commands

| Command | Description |
|---------|-------------|
| `./docker-dev.sh build` | Build the Docker image |
| `./docker-dev.sh dev` | Start development environment and enter shell |
| `./docker-dev.sh run <script>` | Run a specific Python script |
| `./docker-dev.sh install <pkg>` | Install a new Python package |
| `./docker-dev.sh freeze` | Update requirements.txt |
| `./docker-dev.sh clean` | Stop containers and clean up |
| `./docker-dev.sh logs` | Show container logs |
| `./docker-dev.sh jupyter` | Start Jupyter notebook server |

## Docker Configuration

### Dockerfile Features
- Based on `ghcr.io/astral-sh/uv:python3.12-bookworm`
- Installs PyTorch CPU version compatible with Linux
- Includes all necessary system dependencies for video/audio processing
- Uses UV for fast Python package management
- Optimized layer caching for faster rebuilds

### Volume Mounts
- Current directory mounted to `/app`
- Persistent cache directory
- Video and description directories mounted for easy access

## Troubleshooting

### PyTorch Issues
The Docker setup uses PyTorch CPU wheels specifically built for Linux, which resolves the macOS compatibility issues.

### Permission Issues
If you encounter permission issues, ensure Docker Desktop has proper file sharing permissions for your project directory.

### Memory Issues
For large video processing tasks, you may need to increase Docker's memory allocation in Docker Desktop settings.

### Port Conflicts
If port 8000 or 8888 are already in use, modify the port mappings in `docker-compose.yml`.

## Performance Notes

- First build will take longer as it downloads and installs all dependencies
- Subsequent builds are faster due to Docker layer caching
- UV package manager provides faster dependency resolution than pip
- File changes are synced in real-time for development

## Production Deployment

For production deployment, consider:
1. Using multi-stage builds to reduce image size
2. Setting up proper logging and monitoring
3. Using Docker secrets for sensitive configuration
4. Implementing health checks
5. Using a production-grade WSGI server if serving web content
