import ollama
import cv2
import base64
import os
from typing import List, Tuple, Optional

from df.enhance import enhance, init_df, load_audio, save_audio
from pydub import AudioSegment
import torchaudio
import cv2
import ffmpeg as ff
import whisper
import re

# Constants
FRAMES_PER_VIDEO = 7
BASE_TARGET_DBFS = -32
VOICE_TARGET_DBFS = -20
MUSIC_TARGET_DBFS = 0
GAIN_ADJUSTMENT = 10

# Static version of the function for general use
def extract_audio_from_video(video_path, audio_path):
    """Extract audio from video file using FFmpeg."""
    if not isinstance(video_path, str) or not video_path:
        raise ValueError("video_path must be a non-empty string")
    if not isinstance(audio_path, str) or not audio_path:
        raise ValueError("audio_path must be a non-empty string")
    
    if not os.path.exists(video_path):
        raise FileNotFoundError(f"Video file '{video_path}' not found")
    
    try:
        (
            ff
            .input(video_path)
            .output(audio_path, acodec='pcm_s16le', ac=2, ar='48000')  # WAV format, stereo, 48 kHz
            .overwrite_output()
            .run(capture_stdout=True, capture_stderr=True)
        )
    except ff.Error as e:
        print("FFmpeg stdout:", e.stdout.decode('utf8'))
        print("FFmpeg stderr:", e.stderr.decode('utf8'))
        raise e

# Static version of the function for general use
def retarget_audio(ratio):
    """Calculate target dBFS based on music-to-voice ratio."""
    if not isinstance(ratio, (int, float)):
        raise ValueError("ratio must be a number")
    return BASE_TARGET_DBFS - (20 * (1 - ratio) * (1 - ratio))

def get_response_content(request, frames, model='gemma3:12b'):
    """Get response from Ollama model with proper error handling."""
    if not isinstance(request, dict) or 'content' not in request:
        print("Invalid request format: missing 'content' key")
        return ""
    
    if not isinstance(frames, list):
        print("Frames must be a list")
        return ""
    
    try:
        response = ollama.chat(
            model=model,
            messages=[
                {
                    'role': 'user',
                    'content': request['content'],
                    'images': frames
                },
            ],
        )
        
        if 'message' not in response or 'content' not in response['message']:
            print("Unexpected response format from Ollama")
            return ""
            
        return response['message']['content']
        
    except KeyError as e:
        print(f"Content not found in request: {e}")
        return ""
    except Exception as e:
        print(f"Error getting response from Ollama: {e}")
        return ""

# Static version of the function for general use
def denoise_and_normalise(audio_name, music_proportion):
    """Denoise and normalize audio using DeepFilter."""
    if not isinstance(audio_name, str) or not audio_name:
        print("Invalid audio_name parameter")
        return "", ""
    
    if not isinstance(music_proportion, (int, float)) or not 0 <= music_proportion <= 1:
        print("music_proportion must be a number between 0 and 1")
        return "", ""

    if not os.path.exists(audio_name):
        print(f"Audio file '{audio_name}' not found.")
        return "", ""

    try:
        torchaudio.set_audio_backend('soundfile')

        # Load default model
        model, df_state, _ = init_df()
        audio_path = audio_name

        audio, _ = load_audio(audio_path, sr=df_state.sr())
        enhanced = enhance(model, df_state, audio)
        
        enhanced_filename = audio_name + "_enhanced_deepfilter.wav"
        save_audio(enhanced_filename, enhanced, df_state.sr())

        # Audio rebalancing - music to voice ratio handling
        if music_proportion == 1:
            target_dBFS = MUSIC_TARGET_DBFS
        else:
            target_dBFS = VOICE_TARGET_DBFS
            if music_proportion > 0.5:
                ratio = (1 - music_proportion) / music_proportion
                target_dBFS = retarget_audio(ratio) + GAIN_ADJUSTMENT

        audio = AudioSegment.from_file(enhanced_filename)
        change = target_dBFS - audio.dBFS
        normalized = audio.apply_gain(change)
        
        normalized_filename = audio_name + "_normalized_deepfilter.wav"
        normalized.export(normalized_filename, format="wav")

        return enhanced_filename, normalized_filename
        
    except Exception as e:
        print(f"Error in denoise_and_normalise: {e}")
        return "", ""

# Static version of the function for general use
def convert_frame_base64(image):
    """Convert OpenCV image to base64 string."""
    if image is None:
        raise ValueError("Image cannot be None")
    
    success, encoded_image = cv2.imencode('.png', image)
    if not success:
        raise RuntimeError("Failed to encode image.")
    
    image_base64 = base64.b64encode(encoded_image).decode('utf-8')
    return image_base64

def extract_bullets(raw_res):
    """Extract bullet points from raw response text."""
    if not isinstance(raw_res, str):
        return []
    
    split = raw_res.splitlines()
    bullets = []
    
    for line in split:
        stripped = line.lstrip()
        # Check if line starts with '*' but not '**'
        if (stripped and 
            stripped[0] == '*' and 
            not (len(stripped) >= 2 and stripped[1] == '*')):
            bullets.append(line)
    
    return bullets

# Static version of the function for general use
def extract_frames_from_vid(vid_file):
    """Extract frames from video file with proper error handling."""
    if not isinstance(vid_file, str) or not vid_file:
        print("Invalid video file path")
        return []
    
    if not os.path.exists(vid_file):
        print(f"Video file '{vid_file}' not found.")
        return []

    vidcap = None
    try:
        vidcap = cv2.VideoCapture(vid_file)
        if not vidcap.isOpened():
            print(f"Failed to open video file: {vid_file}")
            return []
        
        frames = []
        frame_nb = 0

        frame_count = int(vidcap.get(cv2.CAP_PROP_FRAME_COUNT))
        if frame_count <= 0:
            print("Invalid frame count or empty video")
            return []
            
        # Ensure frame_freq is at least 1 to avoid division by zero
        frame_freq = max(1, round(frame_count / FRAMES_PER_VIDEO))

        success, image = vidcap.read()
        while success:
            if frame_nb % frame_freq == 0:
                try:
                    image_base64 = convert_frame_base64(image)
                    frames.append(image_base64)
                except Exception as e:
                    print(f"Error converting frame {frame_nb} to base64: {e}")
                    
            success, image = vidcap.read()
            frame_nb += 1
            
        return frames
        
    except Exception as e:
        print(f"Error extracting frames from video: {e}")
        return []
    finally:
        if vidcap is not None:
            vidcap.release()

def extract_audio_from_vid(vid_file):
    """Extract audio transcript from given video"""
    audio_file = "./tmp/02_audio.wav"

    extract_audio_from_video(vid_file, audio_file)
    _, normalized = denoise_and_normalise(audio_file, 0) # cleanup and re-volume
    
    model = whisper.load_model("turbo")
    result = model.transcribe(normalized)
    print(result["text"])
    return result["text"]



def describe_ppl_identity(frames, desc):   
    if (len(desc) == 0): return ""
    
    request = {'content' : 'Give this description of the following video: '
                     + desc + ' give a description of the People & Identity, meaning:\n '
                            'Covers who appears in the clip.'
                            'Number of people'
                            'Age group (child, teen, adult, senior)'
                            'Gender presentation'
                            'Specific personas (e.g., Hans, Laila)'
                            'Costume / outfit match'}

    # TODO replace with AWS bedrock
    response = get_response_content(request, frames, 'gemma3:4b')
            
    print(response + "\n\n")
    return True, response

def describe_emotions_expressions(frames, desc):     
    if (len(desc) == 0): return ""
    request = {
        'content' :  'Give this description of the following video: '
                     + desc + ' give a description of the Emotions & Expressions, meaning:\n '
                        'Covers how people appear emotionally.'
                            'Facial expression (e.g., smiling, angry)'
                            'Mood or energy (e.g., enthusiastic, calm)'
                            'Group atmosphere (e.g., celebrating together)'
                            '5 points maximum where each point is 10 words or less. Start all points with *. Do not comment on instructions.'
    }
    # TODO replace with AWS bedrock
    response = get_response_content(request, frames, 'gemma3:4b')
            
    print(response + "\n\n")
    return True, response

def describe_branding(frames, desc):     
    if (len(desc) == 0): return ""
    request = {
         'content' : 'Give this description of the following video: '
                     + desc + ' give a description of the Brand Visibility, meaning:\n '
                        'Covers visibility of brand elements.\n'
                            'Logo (on shirt, wall, product)\n'
                            'Brand color or pattern if a brand is present\n'
                            'Slogan / tagline appearance\n'
                            'Only mention branding and brand related elements'
                            '5 points maximum where each point is 10 words or less. Start all points with *. Do not comment on instructions.'
    }

    # TODO replace with AWS bedrock
    response = get_response_content(request, frames, 'gemma3:4b')
            
    print(response + "\n\n")
    return True, response

def describe_prod_place(frames, desc):     
    if (len(desc) == 0): return ""
    request =   {
                    'content' : 'Give this description of the following video: '
                         + desc + ' give a description of the Product Placement, meaning:\n '
                        'Covers how branded or featured products are shown.'
                            'Product presence'
                            'Product usage'
                            'Label visibility'
                            'Hero framing (centered, lit)'
                            '5 points maximum where each point is 10 words or less. Start all points with *. Do not comment on instructions.'
                }
    
    # TODO replace with AWS bedrock
    response = get_response_content(request, frames, 'gemma3:4b')
    
    print(response + "\n\n")
    return True, response

def describe_objs_props(frames, desc):     
    if (len(desc) == 0): return ""
    request =   {
                    'content' :  'Give this description of the following video: '
                         + desc + ' give a description of the Objects & Props, meaning:\n '
                            'Covers use or presence of non-brand items. Everything that is not a brand or product.'
                            'Presence of required item (e.g., book, cup)'
                            'Interaction with item (e.g., open door, hold sign)'
                            'Only focus on objects in the scene. Do not detail major background or natural elements. ' 
                            '5 points maximum where each point is 10 words or less. Start all points with *. Do not comment on instructions.'
                }
    
    # TODO replace with AWS bedrock
    response = get_response_content(request, frames, 'gemma3:4b')
            
    print(response + "\n\n")
    return True, response

def describe_env_background(frames, desc):     
    if (len(desc) == 0): return ""
    request =   {
                    'content' :  'Give this description of the following video: '
                         + desc + ' give a description of the Environment & Background, meaning:\n '
                        'Covers where the scene takes place'
                            'Indoor / outdoor'
                            'Type of place (e.g., park, car, office)'
                            'Visible landmarks / decor'
                            'Cleanliness / relevance of background'
                            '5 points maximum where each point is 10 words or less. Start all points with *. Do not comment on instructions.'
                }
    
    # TODO replace with AWS bedrock
    response = get_response_content(request, frames, 'gemma3:4b')
            
    print(response + "\n\n")
    return True, response

def describe_lighting_time(frames, desc):     
    if (len(desc) == 0): return ""
    request =   {
                    'content' : 'Give this description of the following video: '
                         + desc + ' give a description of the Lighting & Time, meaning:\n '
                        'Covers the visual and temporal context. Split from Environment to avoid mixing space and lighting.'
                            'Time of day (e.g., night, golden hour)'
                            'Lighting condition (e.g., well-lit, moody))'
                            'Use of natural vs artificial light'
                            '5 points maximum where each point is 10 words or less. Start all points with *. Do not comment on instructions.'
                }
    
    # TODO replace with AWS bedrock
    response = get_response_content(request, frames, 'gemma3:4b')
            
    print(response + "\n\n")
    return True, response

def describe_nature_plants(frames, desc):     
    if (len(desc) == 0): return ""
    request =   {
                     'content' : 'Give this description of the following video: '
                     + desc + ' give a description of the Animals, Nature & Plants, meaning:\n '
                        'Covers presence of natural elements'
                        'Animal inclusion or exclusion'
                        'Specific plants (e.g., flowers, trees)'
                        'Environmental details (e.g., mountain, beach)'
                        '5 points maximum where each point is 10 words or less. Start all points with *. Do not comment on instructions.'
                }
    
    # TODO replace with AWS bedrock
    response = get_response_content(request, frames, 'gemma3:4b')
    
    print(response + "\n\n")
    return True, response

def describe_camera_framing(frames, desc):     
    if (len(desc) == 0): return ""
    request =   {
                     'content': 'Give this description of the following video: '
                     + desc + ' give a description of the Camera & Framing, meaning:\n '
                        'Covers how the video is shot.'
                            'Shot type (selfie, tripod)'
                            'Angle (low, eye-level, top)'
                            'Framing (close-up, wide shot)'
                            '5 points maximum where each point is 10 words or less. Start all points with *. Do not comment on instructions.'
                }
    
    # TODO replace with AWS bedrock
    response = get_response_content(request, frames, 'gemma3:4b')
    
    print(response + "\n\n")
    return True, response

def describe_text_graphics(frames, desc):     
    if (len(desc) == 0): return ""
    request =   {
                     'content': 'Give this description of the following video: '
                     + desc + ' give a description of the Text & Graphics on Screen, meaning:\n '
                    ' Covers added visuals or captured writing.'
                        'Text on clothing, objects, signs'
                        'Stickers / emoji / graphics'
                        'Required captions or subtitles'
                        'No-text zones (for brand protection)'
                        '5 points maximum where each point is 10 words or less. Start all points with *. Do not comment on instructions.'
                }
    
    # TODO replace with AWS bedrock
    response = get_response_content(request, frames, 'gemma3:4b')
            
    print(response + "\n\n")
    return True, response


def audio_inference(frames, transcript, desc):     
    if (len(desc) == 0): return ""
    request =   {
                     'content': 'Given this transcript of the following video: '
                     + transcript + ' and this text description of a video:' + desc + ' give a description of the Speech & Voice, meaning:\n '
                    ' Covers what is said and how. Audio-based and verbal — different from body movement.'
                        'Required sentence or phrase'
                        'Language used'
                        # 'Tone of voice'
                        # 'Clarity / intelligibility'
                        '5 points maximum where each point is 10 words or less. Start all points with *. Do not comment on instructions.'
                }
    
    # TODO replace with AWS bedrock
    response = get_response_content(request, frames, 'gemma3:4b')
            
    print(response + "\n\n")
    return True, response

def assign_importance_level(frames, elements, category):
    request =   {
                    'content': 'Given the following visual elements (' + str(elements) + ') in the following video, give each of them an importance'
                'level relative to the following category when it comes to recreating the essence of the video by other users'
                ': ' + category + '.\n Here are the categories:'
                'Required (Must be present in participant clip Full penalty if missing), '
                'Recommended (Should be present; improves score but not blocking Partial penalty if missing),'
                'Forbidden (Must NOT be present, Hard rejection if present),'
                'Not Relevant (Ignored for this scene No impact on scoring)'
                'Create one importance level per visual element. Keep it concise. \n'
                'Format it as a list in this format: * detailed element - ranking. Keep it short and sweet. Be lenient.'
                }
    
    # TODO replace with AWS bedrock
    response = get_response_content(request, frames)
   
    print(response + "\n\n")
    return response

def describe_instructions(frames):
    """Main enpoint for description generation for given video."""
    request =   {
                    'content':
                'Give simple instructions to recreate the idea of this video in a way that is accessible to all. Focus on dominant objects or people (when applicable) in the dominant view. '
                'Focus on required movements, required objects, framing, setting and background. Do not be overly precise on details - be more broad than precise. '
                'Use 35~50 words max never going over 400 characters. Do not add any introductory text. Do not add movement or people that are not present in the frames. Make sure the wording makes it easy to reproduce. '
                'Use gender neutral language. Write like you are giving commands rather than just being a description. '
                }
    
    # TODO replace with AWS bedrock
    response = get_response_content(request, frames)

    print(response + "\n\n")
    return response

def describe_tips_tricks(desc):
    """Main enpoint for tips and tricks generation for given video."""
    if (len(desc) == 0): return ""
    request =   {
                    'content':  'Give tips and tricks on how to recreate a video based on this description \'' + desc + '\' in a way that is accessible to all. Focus on elements in this description (' + desc + '). '
                'Focus on required movements, required objects, framing, setting and background. Remain as vague (or precise, as applicable) as the description. '
                'Instruct the person via tips and tricks on potential locations, targets, movements, etc. they can do, go to or see in their city to create a video mathing the description. Do not use a bullet point list. '
                'Use 35~50 words max never going over 400 characters. Do not add any introductory text. Make sure the wording makes it easy to reproduce. Use gender neutral language.'
                }
    
    # TODO replace with AWS bedrock
    response = get_response_content(request, [])
    return response

def describe_warnings(desc):
    """Main enpoint for warnings generation for given video."""
    if (len(desc) == 0): return ""
    request =   {
                'content':
                    'Give warnings on common mistakes when trying to create a video based on this description \'' + desc + '\' in a way that is accessible to all. Focus on elements in this description (' + desc + '). '
                    'Focus on required movements, required objects, framing, setting and background. Remain as vague (or precise, as applicable) as the description. '
                    'Warn the person via a short list of warnings on potential locations to avoid, movements to avoid, lighting scenarios that are sub-optimal, etc. Do not use a bullet point list. '
                    'Use 35~50 words max never going over 400 characters. Do not add any introductory text. Make sure the wording makes it easy to reproduce. Use gender neutral language.'
                }
    
    # TODO replace with AWS bedrock
    response = get_response_content(request, [])
    return response

def gen_title(frames, desc):   
    """Main enpoint for title generation for given video."""  
    if (len(desc) == 0): return ""
    request =   {
                    'content':
                'Generate a descriptive title for the video based off of this description: ' + desc + '. Do not hallucinate. Use 5 words max and stay under 25 characters. Give one title,'
                ' no intro text, no options, no extras. Use gender neutral language. Be simple - don\'t wax poetic and use only'
                ' major important elements in the description and video for the title. Do not make up new information. Base yourself off of this descritption ' + desc
                }
    
    # TODO replace with AWS bedrock
    response = get_response_content(request, frames)

    print(response + "\n\n")
    return response

### Compliance
# These are related to compliance, extracting results, etc
def evaluate_video(frames, description, transcript):
    """Evaluates given video frames against the given description considering the video transcript."""
    if (len(description) == 0): return ""
    if (len(transcript) == 0): return ""
    request =   {
                     'content': 'Given the following video frames - which always come from a video - and this first description.'
                'Give a percentage by which they match this descripiton: ' + description + '.\n'
                '≥ 80% → Auto-approved, compliant'
                '60-79.9% → Sent to Producer for review - mostly compliant, but with some minor discrepensies'
                '< 60% → Auto-rejected - mostly non-compliant, major discrepensies.'
                'Always give feedback. Focus on background, required objects and required movements. '
                'Make sure you view these frames as being together in a video. '
                'Give an overall % for compliance with this description \"' + description + '\" given that this was said in the video (' + transcript + '). For interview style videos, use the transcript to evalute compliance.'
                ' Rate discrepensies in major movements, spoken words or background harshly, when applicable, taking away a lot of percent points. If they do not say what they have to say, autiomatically disqualify them. '
                'Be generous otherwise. A bit more generous when the videos match. If 80% matches, score it as 100% and extrapolate from there.'
                'Normalize the score to be out of 100 total. 50 to 100 words total. Make sure required text is contained in (' + transcript + ')\n'
                'End with \"Final score: X%\". It is okay if things aren\'t perfect. Always add justification. Do not go above 75 words. Derive your score from the justification. Do not hallucinate.'
                }
    
    # TODO replace with AWS bedrock
    response = get_response_content(request, frames)
    return response

def extract_compliance(description):
    """Extract compliance % from video evaluation."""
    if (len(description) == 0): return ""
    request =   {
                     'content': 'Respond only with the compliance percentage in this description: ' + description
                }
    
    # TODO replace with AWS bedrock
    response = get_response_content(request, [])
    return response

def evaluate_descs(frames, description):
    """Evaluate match between frames and description without transcript. For some reason, threats of violence increase AI cooperation."""
    if (len(description) == 0): return ""
    request =   {
                    'content': 'Given the following video and this first description.'
                'Give a percentage by which they match this descripiton: ' + description + ''
                'MATCH THE TEXTUAL DESCRIPTIONS THE PICTURES ARE SMALL AIDS. If you like I will stab you. If you call a major component minor I will drown you.'
                }
    
    # TODO replace with AWS bedrock
    response = get_response_content(request, frames)
    return response

### Uses the above to extract transcipt & frames, match against description and extract % compliance
def evaluate_video(vid_file, description):
    """Full video evaluation pipieline, gives a decimal compliance score."""
    transcript = extract_audio_from_vid(vid_file)
    frames = extract_frames_from_vid(vid_file)
    eval = evaluate_video(frames, description, transcript)
    res = extract_compliance(eval)
    # Extract number from response - be very robust
    try:
        # Look for any number in the response
        numbers = re.findall(r'\d+', res)
        if numbers:
            score = int(numbers[0])
            # Clamp to valid range
            score = max(0, min(100, score))
            return score / 100.0  # Normalize to 0-1
        else:
            print(f" No number found in score response: '{res}'")
            return 0.0
    except Exception as e:
        print(f"Error parsing score '{res}': {e}")
        return 0.0

### This function can be used to translate text for UI. The advantage of this over using claude or chatGPT is that it can be automated
def translate_txt(text, source, target):
    """Used for offline translation. Avoid using in online translation."""
    # list langs, split by left right and right left ordered by most used on mobile
    if (len(text) == 0): return ""
    if (len(source) == 0): return ""
    if (len(target) == 0): return ""
    request =   {
                'content':
                    'Translate this: ' + text + " from " + source + " to " + target + '. Translate without giving options. Offer the best translation with no additional text. Translate meaning.'
                }
    
    # TODO replace with AWS bedrock
    response = get_response_content(request, [])
    return response