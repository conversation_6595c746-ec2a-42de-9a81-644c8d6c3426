"""
Improved Video Processing Pipeline with Audio Enhancement and Color Correction
Claude my goat
"""

import os
import tempfile
import logging
from pathlib import Path
from dataclasses import dataclass
from typing import Optional, Tuple
import warnings

# Third-party imports
import cv2
import numpy as np
import ffmpeg as ff
from ffmpeg import Error as FFmpegError
import torchaudio
import librosa
import soundfile as sf
from scipy.signal import butter, lfilter
from pydub import AudioSegment
from skimage.exposure import match_histograms

# DeepFilter imports
try:
    from df.enhance import enhance, init_df, load_audio, save_audio
    DEEPFILTER_AVAILABLE = True
except ImportError:
    DEEPFILTER_AVAILABLE = False
    warnings.warn("DeepFilter not available. Audio denoising will be skipped.")

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


@dataclass
class ProcessingConfig:
    """Configuration for video processing pipeline."""
    audio_sample_rate: int = 48000
    video_codec: str = 'mp4v'
    audio_codec: str = 'pcm_s16le'
    audio_channels: int = 2
    highpass_cutoff: int = 200
    highpass_order: int = 5
    base_music_volume: int = -32
    target_voice_volume: int = -20
    default_fps: float = 30.0
    temp_dir: Optional[str] = None


class VideoProcessingError(Exception):
    """Custom exception for video processing errors."""
    pass


class VideoProcessor:
    """Video processing pipeline with audio enhancement and color correction."""
    
    def __init__(self, config: Optional[ProcessingConfig] = None):
        self.config = config or ProcessingConfig()
        self.temp_dir = Path(self.config.temp_dir or tempfile.gettempdir()) / "video_processing"
        self.temp_dir.mkdir(exist_ok=True, parents=True)
        
        # Initialize DeepFilter model if available
        self.df_model = None
        self.df_state = None
        if DEEPFILTER_AVAILABLE:
            try:
                self.df_model, self.df_state, _ = init_df()
                logger.info("DeepFilter model initialized successfully")
            except Exception as e:
                logger.warning(f"Failed to initialize DeepFilter: {e}")
                self.df_model = None
                self.df_state = None
        
        # Set audio backend
        try:
            torchaudio.set_audio_backend('soundfile')
        except Exception:
            pass  # Backend setting is deprecated in newer versions
    
    def validate_file(self, filepath: Path, file_type: str = "File") -> None:
        """Validate that a file exists and is readable."""
        if not filepath.exists():
            raise FileNotFoundError(f"{file_type} '{filepath}' not found")
        if not filepath.is_file():
            raise ValueError(f"{file_type} '{filepath}' is not a valid file")
    
    def get_video_properties(self, video_path: Path) -> dict:
        """Extract video properties using ffprobe."""
        try:
            probe = ff.probe(str(video_path))
            video_stream = next((s for s in probe['streams'] if s['codec_type'] == 'video'), None)
            audio_stream = next((s for s in probe['streams'] if s['codec_type'] == 'audio'), None)
            
            if not video_stream:
                raise VideoProcessingError("No video stream found")
            
            # Parse frame rate
            fps = self.config.default_fps
            if 'r_frame_rate' in video_stream:
                try:
                    fps_str = video_stream['r_frame_rate']
                    if '/' in fps_str:
                        num, den = map(int, fps_str.split('/'))
                        fps = num / den if den != 0 else self.config.default_fps
                except (ValueError, ZeroDivisionError):
                    logger.warning(f"Could not parse frame rate, using default: {fps}")
            
            return {
                'fps': fps,
                'duration': float(probe['format']['duration']),
                'width': int(video_stream.get('width', 0)),
                'height': int(video_stream.get('height', 0)),
                'has_audio': audio_stream is not None
            }
        except Exception as e:
            raise VideoProcessingError(f"Failed to get video properties: {e}")
    
    def extract_audio_from_video(self, video_path: Path, output_path: Path) -> None:
        """Extract audio from video file."""
        try:
            self.validate_file(video_path, "Video file")
            
            logger.info(f"Extracting audio from {video_path}")
            (
                ff.input(str(video_path))
                .output(
                    str(output_path),
                    acodec=self.config.audio_codec,
                    ac=self.config.audio_channels,
                    ar=str(self.config.audio_sample_rate)
                )
                .overwrite_output()
                .run(capture_stdout=True, capture_stderr=True, quiet=True)
            )
            logger.info(f"Audio extracted to {output_path}")
            
        except ff.Error as e:
            error_msg = e.stderr.decode('utf-8') if e.stderr else "Unknown FFmpeg error"
            raise VideoProcessingError(f"Failed to extract audio: {error_msg}")
    
    def denoise_and_normalize_audio(self, audio_path: Path, music_proportion: float) -> Tuple[Path, Path]:
        """Denoise and normalize audio using DeepFilter and volume adjustment."""
        self.validate_file(audio_path, "Audio file")
        
        enhanced_path = self.temp_dir / f"{audio_path.stem}_enhanced.wav"
        normalized_path = self.temp_dir / f"{audio_path.stem}_normalized.wav"
        
        # DeepFilter denoising if available
        if self.df_model and self.df_state:
            logger.info("Applying DeepFilter noise reduction")
            try:
                audio, _ = load_audio(str(audio_path), sr=self.df_state.sr())
                enhanced_audio = enhance(self.df_model, self.df_state, audio)
                save_audio(str(enhanced_path), enhanced_audio, self.df_state.sr())
            except Exception as e:
                logger.warning(f"DeepFilter failed, using original audio: {e}")
                enhanced_path = audio_path
        else:
            logger.info("DeepFilter not available, skipping noise reduction")
            enhanced_path = audio_path
        
        # Normalize volume
        target_dbfs = self.calculate_voice_volume(music_proportion)
        logger.info(f"Normalizing audio to {target_dbfs} dBFS")
        
        try:
            audio_segment = AudioSegment.from_file(str(enhanced_path))
            change = target_dbfs - audio_segment.dBFS
            normalized = audio_segment.apply_gain(change)
            normalized.export(str(normalized_path), format="wav")
            logger.info(f"Audio normalized and saved to {normalized_path}")
            
        except Exception as e:
            raise VideoProcessingError(f"Failed to normalize audio: {e}")
        
        return enhanced_path, normalized_path
    
    def calculate_voice_volume(self, music_proportion: float) -> float:
        """Calculate target volume for voice based on music proportion."""
        if music_proportion == 1.0:
            return 0  # Music only, no voice adjustment needed
        elif music_proportion > 0.5:
            ratio = (1 - music_proportion) / music_proportion
            return self.calculate_music_volume_adjustment(ratio) + 10
        else:
            return self.config.target_voice_volume
    
    def calculate_music_volume_adjustment(self, ratio: float) -> float:
        """Calculate volume adjustment for music based on voice-to-music ratio."""
        return self.config.base_music_volume - (20 * (1 - ratio) * (1 - ratio))
    
    def recolor_video(self, video_path: Path, reference_image_path: Optional[Path] = None) -> Path:
        """Apply color correction to video using histogram matching."""
        self.validate_file(video_path, "Video file")
        
        if not reference_image_path or not reference_image_path.exists():
            logger.info("No reference image provided or found, skipping color correction")
            return video_path
        
        logger.info(f"Applying color correction using reference: {reference_image_path}")
        
        # Load reference image
        try:
            reference_img = cv2.imread(str(reference_image_path))
            if reference_img is None:
                raise ValueError("Could not load reference image")
        except Exception as e:
            logger.warning(f"Failed to load reference image: {e}")
            return video_path
        
        # Get video properties
        props = self.get_video_properties(video_path)
        output_path = self.temp_dir / f"{video_path.stem}_recolored.mp4"
        
        # Process video
        vidcap = cv2.VideoCapture(str(video_path))
        if not vidcap.isOpened():
            raise VideoProcessingError(f"Could not open video: {video_path}")
        
        try:
            # Setup video writer
            fourcc = cv2.VideoWriter_fourcc(*'mp4v')
            out = cv2.VideoWriter(
                str(output_path),
                fourcc,
                props['fps'],
                (props['width'], props['height'])
            )
            
            frame_count = 0
            while True:
                ret, frame = vidcap.read()
                if not ret:
                    break
                
                # Apply histogram matching
                try:
                    corrected_frame = match_histograms(frame, reference_img, channel_axis=-1)
                    out.write(corrected_frame.astype(np.uint8))
                except Exception as e:
                    logger.warning(f"Failed to process frame {frame_count}: {e}")
                    out.write(frame)  # Use original frame
                
                frame_count += 1
                if frame_count % 100 == 0:
                    logger.info(f"Processed {frame_count} frames")
            
            logger.info(f"Color correction completed. Processed {frame_count} frames")
            
        except Exception as e:
            raise VideoProcessingError(f"Failed during video recoloring: {e}")
        finally:
            vidcap.release()
            out.release()
            cv2.destroyAllWindows()
        
        return output_path
    
    def replace_video_audio(self, video_path: Path, audio_path: Path, output_path: Path) -> None:
        """Replace video audio with processed audio."""
        try:
            self.validate_file(video_path, "Video file")
            self.validate_file(audio_path, "Audio file")
            
            logger.info(f"Replacing audio in video: {video_path}")
            video_input = ff.input(str(video_path))
            audio_input = ff.input(str(audio_path))
            
            (
                ff.concat(video_input, audio_input, v=1, a=1)
                .output(str(output_path))
                .run(overwrite_output=True, quiet=True)
            )
            logger.info(f"Video with new audio saved to: {output_path}")
            
        except ff.Error as e:
            error_msg = e.stderr.decode('utf-8') if e.stderr else "Unknown FFmpeg error"
            raise VideoProcessingError(f"Failed to replace audio: {error_msg}")
    
    def apply_highpass_filter(self, audio_path: Path, output_path: Path) -> None:
        """Apply highpass filter to audio file."""
        try:
            self.validate_file(audio_path, "Audio file")
            
            logger.info(f"Applying highpass filter to {audio_path}")
            y, sr = librosa.load(str(audio_path), sr=None)
            
            # Design highpass filter
            nyq = 0.5 * sr
            norm_cutoff = self.config.highpass_cutoff / nyq
            b, a = butter(self.config.highpass_order, norm_cutoff, btype='high', analog=False)
            
            # Apply filter
            filtered = lfilter(b, a, y)
            
            # Save filtered audio
            sf.write(str(output_path), filtered, sr)
            logger.info(f"Highpass filtered audio saved to: {output_path}")
            
        except Exception as e:
            raise VideoProcessingError(f"Failed to apply highpass filter: {e}")
    
    def add_background_music(self, video_path: Path, music_path: Path, 
                           music_proportion: float, output_path: Path) -> None:
        """Add background music to video with specified proportion."""
        if music_proportion == 0:
            # No music, just copy the video
            logger.info("No background music requested, copying video")
            try:
                (
                    ff.input(str(video_path))
                    .output(str(output_path), vcodec='copy', acodec='copy')
                    .run(overwrite_output=True, quiet=True)
                )
            except ff.Error as e:
                raise VideoProcessingError(f"Failed to copy video: {e}")
            return
        
        self.validate_file(video_path, "Video file")
        self.validate_file(music_path, "Music file")
        
        # Get video duration
        props = self.get_video_properties(video_path)
        duration_ms = int(props['duration'] * 1000)
        
        # Process music
        temp_music_path = self.temp_dir / f"processed_music_{music_path.stem}.wav"
        
        # Apply highpass filter if music proportion is low
        if music_proportion <= 0.5:
            logger.info("Applying highpass filter to music")
            self.apply_highpass_filter(music_path, temp_music_path)
        else:
            temp_music_path = music_path
        
        # Adjust music volume and duration
        try:
            logger.info("Processing background music")
            music_audio = AudioSegment.from_file(str(temp_music_path))
            
            # Trim or loop music to match video duration
            if len(music_audio) > duration_ms:
                music_audio = music_audio[:duration_ms]
            elif len(music_audio) < duration_ms:
                # Loop the music to match duration
                loops_needed = (duration_ms // len(music_audio)) + 1
                music_audio = music_audio * loops_needed
                music_audio = music_audio[:duration_ms]
            
            # Adjust volume
            if music_proportion == 1.0:
                target_dbfs = self.config.target_voice_volume
            else:
                ratio = music_proportion / (1 - music_proportion)
                target_dbfs = self.calculate_music_volume_adjustment(ratio)
                if music_proportion > 0.5:
                    target_dbfs = self.config.target_voice_volume
            
            change = target_dbfs - music_audio.dBFS
            normalized_music = music_audio.apply_gain(change)
            
            # Save processed music
            final_music_path = self.temp_dir / "final_music.wav"
            normalized_music.export(str(final_music_path), format="wav")
            
        except Exception as e:
            raise VideoProcessingError(f"Failed to process music: {e}")
        
        # Mix audio
        try:
            logger.info("Mixing video audio with background music")
            video_input = ff.input(str(video_path))
            music_input = ff.input(str(final_music_path))
            
            merged_audio = ff.filter([video_input.audio, music_input], 'amix')
            
            (
                ff.concat(video_input, merged_audio, v=1, a=1)
                .output(str(output_path))
                .run(overwrite_output=True, quiet=True)
            )
            logger.info(f"Final video with background music saved to: {output_path}")
            
        except ff.Error as e:
            error_msg = e.stderr.decode('utf-8') if e.stderr else "Unknown FFmpeg error"
            raise VideoProcessingError(f"Failed to mix audio: {error_msg}")
    
    def process_video(self, input_video: Path, output_video: Path,
                     music_file: Optional[Path] = None, music_proportion: float = 0.0,
                     reference_image: Optional[Path] = None) -> None:
        """
        Main processing pipeline for video enhancement.
        
        Args:
            input_video: Path to input video file
            output_video: Path to output video file
            music_file: Optional path to background music file
            music_proportion: Music volume proportion (0.0 = no music, 1.0 = music only)
            reference_image: Optional path to reference image for color correction
        """
        try:
            logger.info("Starting video processing pipeline")
            
            # Validate inputs
            self.validate_file(input_video, "Input video")
            if music_file and music_proportion > 0:
                self.validate_file(music_file, "Music file")
            
            # Step 1: Extract audio
            audio_file = self.temp_dir / "extracted_audio.wav"
            self.extract_audio_from_video(input_video, audio_file)
            
            # Step 2: Process audio (denoise and normalize)
            if music_proportion < 1.0:  # Only process if we need voice audio
                _, normalized_audio = self.denoise_and_normalize_audio(audio_file, music_proportion)
            else:
                normalized_audio = music_file  # Use music as main audio
            
            # Step 3: Color correction
            recolored_video = self.recolor_video(input_video, reference_image)
            
            # Step 4: Replace audio in recolored video
            video_with_new_audio = self.temp_dir / "video_with_processed_audio.mp4"
            self.replace_video_audio(recolored_video, normalized_audio, video_with_new_audio)
            
            # Step 5: Add background music
            if music_file and music_proportion > 0:
                self.add_background_music(video_with_new_audio, music_file, music_proportion, output_video)
            else:
                # No music, just copy the processed video
                try:
                    (
                        ff.input(str(video_with_new_audio))
                        .output(str(output_video), vcodec='copy', acodec='copy')
                        .run(overwrite_output=True, quiet=True)
                    )
                except ff.Error as e:
                    raise VideoProcessingError(f"Failed to copy final video: {e}")
            
            logger.info(f"Video processing completed successfully: {output_video}")
            
        except Exception as e:
            logger.error(f"Video processing failed: {e}")
            raise
    
    def cleanup_temp_files(self) -> None:
        """Clean up temporary files."""
        try:
            import shutil
            if self.temp_dir.exists():
                shutil.rmtree(self.temp_dir)
                logger.info("Temporary files cleaned up")
        except Exception as e:
            logger.warning(f"Failed to clean up temporary files: {e}")


def demo():
    """Example usage of the VideoProcessor."""
    # Configuration
    config = ProcessingConfig(
        audio_sample_rate=48000,
        target_voice_volume=-20,
        base_music_volume=-32
    )
    
    # Create processor
    processor = VideoProcessor(config)
    
    try:
        # Example processing
        vid_name = "2tu"
        input_video = Path("./testvids/" + vid_name + ".mp4")
        output_video = Path("./output/final_processed_video_" + vid_name + ".mp4")
        music_file = Path("./hyper_camelot.wav")
        # reference_image = Path("./02-histogram.jpg")
        
        # Ensure output directory exists
        output_video.parent.mkdir(exist_ok=True, parents=True)
        
        # Process video
        processor.process_video(
            input_video=input_video,
            output_video=output_video,
            music_file=music_file,
            music_proportion=0.8,
            reference_image=""
        )
        
    except Exception as e:
        logger.error(f"Processing failed: {e}")
    finally:
        # Clean up
        processor.cleanup_temp_files()

# for a demo
if __name__ == "__main__":
    demo()