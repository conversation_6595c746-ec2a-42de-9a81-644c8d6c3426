# Video Matcher with Audio Analysis - Enhanced Edition
# Includes visual frames + audio transcription analysis

import os
import ollama
from pathlib import Path
from typing import List, Dict, Tuple, Optional
import cv2
import re
from dataclasses import dataclass
import time
import base64
import whisper
from df.enhance import enhance, init_df, load_audio, save_audio
from pydub import AudioSegment
import torchaudio
import cv2
import ffmpeg as ff
import VideoAnalysisAI as VAI

@dataclass
class VideoCandidate:
    """Data class for video candidates"""
    filename: str
    path: str
    description: str
    audio_transcript: str
    score: float
    file_size: int

class VideoMatcher:
    def __init__(self, video_dir: str, descriptions_dir: str, model_name: str = "gemma3:14b", 
                 use_audio: bool = False):
        """
        Initialize VideoMatcher with optional audio analysis
        
        Args:
            video_dir: Directory containing MP4 files
            descriptions_dir: Directory containing TXT description files  
            model_name: LLM model name (gemma3:14b, gemma3:12b, etc.)
            use_audio: Whether to enable audio transcription analysis
        """
        self.video_dir = Path(video_dir)
        self.descriptions_dir = Path(descriptions_dir)
        self.model_name = model_name
        self.use_audio = use_audio
        
        print(f"🎬 VideoMatcher initialized")
        print(f"📁 Video directory: {self.video_dir}")
        print(f"📄 Descriptions directory: {self.descriptions_dir}")
        print(f"🤖 Using model: {self.model_name}")
        print(f"🎵 Audio analysis: {'Enabled' if use_audio else 'Disabled'}")
        
        # Initialize Whisper model if audio analysis is enabled
        if self.use_audio:
            try:
                print("🎵 Loading Whisper model...")
                self.whisper_model = whisper.load_model("turbo")
                print("✅ Whisper model loaded successfully")
            except Exception as e:
                print(f"❌ Failed to load Whisper model: {e}")
                print("🔄 Disabling audio analysis")
                self.use_audio = False
                self.whisper_model = None
        else:
            self.whisper_model = None
        
        # Test LLM connection
        self._test_llm_connection()
    
    def _test_llm_connection(self):
        """Test if LLM is working and model is available"""
        try:
            # Test the custom function
            test_request = {"content": "Say 'OK' if you can hear me"}
            test_response = VAI.get_response_content(test_request, [], self.model_name)
            
            if test_response:
                print(f"✅ LLM connected. Test response: {test_response[:50]}...")
            else:
                print("⚠️  LLM responded but with empty content")
            
        except Exception as e:
            print(f"❌ LLM connection failed: {e}")
            print("💡 Make sure ollama is installed and running")
            raise
    
    def get_video_files(self) -> List[Path]:
        """Get all MP4 files in video directory"""
        video_files = list(self.video_dir.glob("*.mp4"))
        print(f"📹 Found {len(video_files)} video files")
        return video_files
    
    def get_description(self, video_path: Path) -> str:
        """Get text description for a video file"""
        # Try multiple naming conventions
        possible_desc_files = [
            self.descriptions_dir / f"{video_path.stem}.txt",
            self.descriptions_dir / f"{video_path.name}.txt",
            self.descriptions_dir / f"{video_path.name.replace('.mp4', '.txt')}",
            self.descriptions_dir / f"{video_path.name.replace('.MP4', '.txt')}"
        ]
        
        for desc_file in possible_desc_files:
            if desc_file.exists():
                try:
                    content = desc_file.read_text(encoding='utf-8').strip()
                    if content:  # Make sure it's not empty
                        return content
                except Exception as e:
                    print(f"⚠️  Error reading {desc_file}: {e}")
                    continue
        
        # Fallback description
        return f"Video file: {video_path.name} (no description available)"
    
    def extract_and_transcribe_audio(self, video_path: Path) -> str:
        """Extract and transcribe audio from video"""
        if not self.use_audio or not self.whisper_model:
            return ""
        
        try:
            print(f"  🎵 Extracting audio from {video_path.name}...")
            transcript = VAI.extract_audio_from_vid(str(video_path))
            
            if transcript and transcript.strip():
                print(f"  ✅ Audio transcribed: {len(transcript)} characters")
                return transcript.strip()
            else:
                print(f"  ⚠️  No audio content transcribed")
                return ""
                
        except Exception as e:
            print(f"  ❌ Audio extraction failed: {e}")
            return ""
    
    def call_llm(self, prompt: str, frames: List = None) -> str:
        """Call LLM using the custom function"""
        if frames is None:
            frames = []
        
        request = {"content": prompt}
        response = VAI.get_response_content(request, frames, self.model_name)
        return response.strip() if response else "0"
    
    def score_video_multimodal(self, description: str, audio_transcript: str, 
                              user_prompt: str, frames: List[str] = None) -> float:
        """Score video using text description, audio transcript, and optional frames"""
        
        # Build comprehensive content description
        content_parts = []
        
        if description:
            content_parts.append(f"Text Description: {description}")
        
        if audio_transcript:
            content_parts.append(f"Audio Transcript: {audio_transcript}")
        
        if not content_parts:
            return 0.0
        
        combined_content = "\n\n".join(content_parts)
        
        # Enhanced scoring prompt that considers all modalities
        scoring_prompt = f"""You are a video search expert. Rate how well this video content matches the user's search request.

USER SEARCH: "{user_prompt}"

VIDEO CONTENT:
{combined_content}

{"You can also see visual frames from this video." if frames else ""}

Rate the match from 0-100 considering ALL available information:
- Text description relevance to search
- Audio content relevance to search (spoken words, dialogue, sounds)
{"- Visual content relevance to search" if frames else ""}
- Overall semantic match with user intent

Scoring scale:
- 100 = Perfect match (content exactly matches user request)
- 80-99 = Very good match (highly relevant across modalities)
- 60-79 = Good match (relevant in some aspects)
- 40-59 = Moderate match (partially relevant)
- 20-39 = Poor match (barely relevant)
- 0-19 = No match (completely irrelevant)

Consider:
- Semantic similarity (not just keyword matching)
- Context and intent of the user's request
- Quality and relevance of available information
- Multi-modal alignment (do audio, visual, text tell the same story?)

Respond with ONLY the number (0-100). No explanation.

SCORE:"""
        
        score_text = self.call_llm(scoring_prompt, frames if frames else [])
        
        # Extract number from response
        try:
            numbers = re.findall(r'\d+', score_text)
            if numbers:
                score = int(numbers[0])
                score = max(0, min(100, score))
                return score / 100.0
            else:
                print(f"⚠️  No number found in multimodal score: '{score_text}'")
                return 0.0
        except Exception as e:
            print(f"⚠️  Error parsing multimodal score: {e}")
            return 0.0
    
    def score_video_description(self, description: str, user_prompt: str) -> float:
        """Score how well a video description matches the user prompt (text only)"""
        
        scoring_prompt = f"""You are a video search expert. Rate how well this video description matches the user's search request.

USER SEARCH: "{user_prompt}"

VIDEO DESCRIPTION: "{description}"

Rate the match from 0-100 where:
- 100 = Perfect match, exactly what user wants  
- 80-99 = Very good match, highly relevant
- 60-79 = Good match, somewhat relevant
- 40-59 = Moderate match, partially relevant  
- 20-39 = Poor match, barely relevant
- 0-19 = No match, completely irrelevant

Consider semantic meaning, not just keywords. A video about "ocean waves" matches "sea footage" even without exact words.

Respond with ONLY the number (0-100). No explanation needed.

SCORE:"""
        
        score_text = self.call_llm(scoring_prompt)
        
        # Extract number from response - be very robust
        try:
            # Look for any number in the response
            numbers = re.findall(r'\d+', score_text)
            if numbers:
                score = int(numbers[0])
                # Clamp to valid range
                score = max(0, min(100, score))
                return score / 100.0  # Normalize to 0-1
            else:
                print(f"⚠️  No number found in score response: '{score_text}'")
                return 0.0
        except Exception as e:
            print(f"⚠️  Error parsing score '{score_text}': {e}")
            return 0.0
    
    def extract_desc_and_frames(self, video_path: Path, original_description: str) -> Tuple[str, List[str]]:
        """REMOVED DESCRIPTION ENHANCEMENT - ONLY EXTRACTS FRAMES (done to cut down on inference time)\n
        Old: Enhance description using extracted frames and LLM vision\n
        Restore old version for decription enhancement"""
        try:
            print(f"  🖼️  Extracting frames for {video_path.name}...")
            frames = VAI.extract_frames_from_vid(str(video_path))
            
            if not frames or len(frames) == 0:
                print(f"  ⚠️  No frames extracted")
                return original_description, []
            
            print(f"  ✅ Extracted {len(frames)} frames")
            
            # # Analyze frames with LLM vision
            # frame_analysis = self._analyze_frames_with_vision(original_description, frames[:3])
            
            # if frame_analysis and frame_analysis.strip():
            #     enhanced = f"{original_description}\n\nVisual analysis: {frame_analysis}"
            #     return enhanced, frames
            # else:
            return original_description, frames
                
        except Exception as e:
            print(f"  ❌ Error enhancing description: {e}")
            return original_description, []
    
    def _analyze_frames_with_vision(self, description: str, frames: List[str]) -> str:
        """Analyze frames using LLM vision capabilities"""
        try:
            analysis_prompt = f"""Look at these video frames and enhance the existing description.

EXISTING DESCRIPTION: "{description}"

Analyze the visual content in these {len(frames)} frames and provide additional details about:
- Visual style and mood
- Colors and lighting
- Actions or movement visible
- Setting and environment
- People and their expressions/emotions
- Objects and their context
- Any notable visual elements

Keep your analysis brief (2-3 sentences) and focus on details that would help match this video to search queries.

VISUAL ANALYSIS:"""
            
            return self.call_llm(analysis_prompt, frames)
                
        except Exception as e:
            print(f"    ⚠️  Error in visual frame analysis: {e}")
            return ""
    
    def enhance_description_with_audio(self, video_path: Path, original_description: str, 
                                     audio_transcript: str) -> str:
        """Enhance description by analyzing audio transcript"""
        if not audio_transcript or not audio_transcript.strip():
            return original_description
        
        try:
            enhancement_prompt = f"""Analyze this video's audio transcript to enhance the existing description.

EXISTING DESCRIPTION: "{original_description}"

AUDIO TRANSCRIPT: "{audio_transcript}"

Based on the spoken content, enhance the description by adding relevant details about:
- What people are saying or discussing
- Emotional tone of speech (happy, sad, excited, etc.)
- Language(s) spoken
- Key topics or subjects mentioned
- Any music, sounds, or ambient audio
- Context that helps understand the video content

Provide 1-2 sentences that would help match this video to search queries. Focus on content that complements the visual description.

AUDIO ENHANCEMENT:"""
            
            audio_analysis = self.call_llm(enhancement_prompt)
            
            if audio_analysis and audio_analysis.strip():
                enhanced = f"{original_description}\n\nAudio content: {audio_analysis}"
                return enhanced
            else:
                return original_description
                
        except Exception as e:
            print(f"    ⚠️  Error in audio analysis: {e}")
            return original_description
    
    def find_best_match(self, user_prompt: str, use_frames: bool = False, 
                       use_audio: bool = None, top_k: int = 5) -> Tuple[str, Dict]:
        """
        Find the video that best matches the user prompt
        
        Args:
            user_prompt: User's search query
            use_frames: Whether to enhance descriptions with frame analysis  
            use_audio: Whether to use audio analysis (None = use class default)
            top_k: Number of top candidates to return
            
        Returns:
            Tuple of (best_video_name, complete_results_dict)
        """
        print(f"\n🔍 Searching for: '{user_prompt}'")
        print("=" * 50)
        
        # Determine if audio should be used
        if use_audio is None:
            use_audio = self.use_audio
        elif use_audio and not self.use_audio:
            print("⚠️  Audio requested but not initialized, disabling audio analysis")
            use_audio = False
        
        video_files = self.get_video_files()
        
        if not video_files:
            raise ValueError("❌ No MP4 files found in video directory")
        
        candidates = []
        
        for i, video_path in enumerate(video_files):
            print(f"📹 Processing {i+1}/{len(video_files)}: {video_path.name}")
            
            # Get basic description
            description = self.get_description(video_path)
            print(f"  📝 Description: {description[:100]}...")
            
            # Extract audio transcript if enabled
            audio_transcript = ""
            if use_audio:
                audio_transcript = self.extract_and_transcribe_audio(video_path)
            
            # Process based on enabled modalities
            if use_frames and use_audio:
                # Full multimodal analysis
                print(f"  🎬 Full multimodal analysis...")
                
                enhanced_description, frames = self.extract_desc_and_frames(video_path, description)
                if audio_transcript:
                    enhanced_description = self.enhance_description_with_audio(
                        video_path, enhanced_description, audio_transcript
                    )
                
                score = self.score_video_multimodal(
                    enhanced_description, audio_transcript, user_prompt, frames
                )
                final_description = enhanced_description
                
            elif use_frames:
                # Visual + text analysis
                print(f"  🖼️  Visual analysis...")
                enhanced_description, frames = self.extract_desc_and_frames(video_path, description)
                score = self.score_video_multimodal(
                    enhanced_description, "", user_prompt, frames
                )
                final_description = enhanced_description
                
            elif use_audio:
                # Audio + text analysis  
                print(f"  🎵 Audio analysis...")
                if audio_transcript:
                    enhanced_description = self.enhance_description_with_audio(
                        video_path, description, audio_transcript
                    )
                else:
                    enhanced_description = description
                score = self.score_video_multimodal(
                    enhanced_description, audio_transcript, user_prompt
                )
                final_description = enhanced_description
                
            else:
                # Text-only analysis
                print(f"  🤖 Text-only scoring...")
                score = self.score_video_description(description, user_prompt)
                final_description = description
            
            print(f"  📊 Score: {score:.3f}")
            
            # Create candidate
            candidate = VideoCandidate(
                filename=video_path.name,
                path=str(video_path),
                description=final_description,
                audio_transcript=audio_transcript,
                score=score,
                file_size=video_path.stat().st_size
            )
            candidates.append(candidate)
            print(f"  ✅ Done\n")
        
        # Sort by score (highest first)
        candidates.sort(key=lambda x: x.score, reverse=True)
        
        if not candidates:
            raise ValueError("❌ No suitable videos found")
        
        best_candidate = candidates[0]
        
        # Prepare complete results
        results = {
            "best_match": {
                "filename": best_candidate.filename,
                "path": best_candidate.path,
                "description": best_candidate.description,
                "audio_transcript": best_candidate.audio_transcript,
                "score": best_candidate.score,
                "file_size": best_candidate.file_size
            },
            "candidate_ranking": [
                {
                    "rank": i + 1,
                    "filename": candidate.filename,
                    "score": candidate.score,
                    "description": candidate.description,
                    "audio_transcript": candidate.audio_transcript,
                    "path": candidate.path
                }
                for i, candidate in enumerate(candidates)
            ],
            "search_summary": {
                "query": user_prompt,
                "total_videos_analyzed": len(video_files),
                "best_score": best_candidate.score,
                "used_frame_analysis": use_frames,
                "used_audio_analysis": use_audio,
                "modalities_used": self._get_modalities_used(use_frames, use_audio)
            }
        }
        
        # Print results
        print("🏆 RESULTS")
        print("=" * 50)
        print(f"🥇 Best match: {best_candidate.filename}")
        print(f"📊 Score: {best_candidate.score:.3f}")
        print(f"📝 Description: {best_candidate.description[:150]}...")
        if best_candidate.audio_transcript:
            print(f"🎵 Audio: {best_candidate.audio_transcript[:100]}...")
        print(f"\n📋 Top {min(top_k, len(candidates))} candidates:")
        
        for i, candidate in enumerate(candidates[:top_k]):
            emoji = ["🥇", "🥈", "🥉", "4️⃣", "5️⃣"][i] if i < 5 else f"{i+1}️⃣"
            audio_indicator = " 🎵" if candidate.audio_transcript else ""
            print(f"  {emoji} {candidate.filename} (score: {candidate.score:.3f}){audio_indicator}")
        
        return best_candidate.filename, results
    
    def _get_modalities_used(self, use_frames: bool, use_audio: bool) -> List[str]:
        """Get list of modalities used in analysis"""
        modalities = ["text"]
        if use_frames:
            modalities.append("visual")
        if use_audio:
            modalities.append("audio")
        return modalities


# Main API function with audio support
def find_best_video(prompt: str, video_dir: str = "videos", descriptions_dir: str = "descriptions", 
                   use_frames: bool = False, use_audio: bool = False, 
                   model_name: str = "gemma3:14b", top_k: int = 5) -> Tuple[str, Dict]:
    """
    🎯 Main API function - find best matching video with multimodal analysis
    
    Args:
        prompt: What you're looking for ("ocean waves", "celebration", etc.)
        video_dir: Folder with your MP4 files
        descriptions_dir: Folder with your TXT description files
        use_frames: Whether to analyze visual frames for better matching
        use_audio: Whether to analyze audio/speech content for better matching
        model_name: LLM model to use (gemma3:14b, gemma3:12b, etc.)
        top_k: Number of top results to return
        
    Returns:
        (best_video_filename, complete_results_dict)
    """
    matcher = VideoMatcher(video_dir, descriptions_dir, model_name, use_audio=use_audio)
    return matcher.find_best_match(prompt, use_frames, use_audio, top_k=top_k)


# Enhanced demo with audio
def demo():
    """Run a demo with multimodal analysis"""
    print("🚀 MULTIMODAL VIDEO MATCHER DEMO")
    print("=" * 60)
    
    # Test with sample prompts
    # 2min per vid per prompt on a 9950x3D and 9070XT (16GB VRAM, 32GB RAM)
    test_prompts = [
        "show your favorite local landmark",
        "people speaking in German", 
        "joyful celebration",
        "women in gym clothes",
        "replace the video with a south-east asian man"
    ]
    
    for prompt in test_prompts:
        try:
            print(f"\n🎬 TESTING: '{prompt}'")
            print("-" * 40)
            
            video_name, results = find_best_video(
                prompt=prompt,
                video_dir="./testvids",
                descriptions_dir="./testdescs",
                use_frames=True,    # Visual analysis
                use_audio=True,     # Audio analysis  
                model_name="gemma3:12b",
                top_k=5
            )
            
            print(f"✅ SUCCESS: Found {video_name}")
            
        except Exception as e:
            print(f"❌ ERROR: {e}")

# for a demo
if __name__ == "__main__":
    demo()