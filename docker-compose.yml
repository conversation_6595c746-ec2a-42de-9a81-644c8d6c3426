version: '3.8'

services:
  yiqqi-ai-workflows:
    build: .
    container_name: yiqqi-ai-workflows
    volumes:
      # Mount the current directory to /app, but exclude .venv
      - .:/app
      # Create anonymous volume for .venv to prevent conflicts
      - /app/.venv
      # Mount specific directories for persistent data
      - ./cache:/app/cache
      - ./testvids:/app/testvids
      - ./testdescs:/app/testdescs
      - ./demofiles:/app/demofiles
    environment:
      - PYTHONPATH=/app
      - UV_SYSTEM_PYTHON=1
    ports:
      - "8000:8000"
    # Override default command for development
    command: /bin/bash
    stdin_open: true
    tty: true
    
    # Development configuration with watch (requires Docker Compose 2.22+)
    develop:
      watch:
        # Sync the working directory with the /app directory in the container
        - action: sync
          path: .
          target: /app
          # Exclude the project virtual environment and other unnecessary files
          ignore:
            - .venv/
            - __pycache__/
            - .git/
            - .DS_Store
            - "*.pyc"
            - "*.pyo"
            - "*.pyd"
            - ".Python"
            - "env/"
            - "venv/"
            - ".env"
            - ".gitignore"
            - "README.md"
            - "Dockerfile"
            - "docker-compose.yml"

        # Rebuild the image on changes to requirements
        - action: rebuild
          path: ./requirements.txt
