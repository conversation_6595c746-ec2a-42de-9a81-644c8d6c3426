#!/bin/bash

# Docker Development Helper Script for YIQQI AI Workflows

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if Dock<PERSON> is running
check_docker() {
    if ! docker info > /dev/null 2>&1; then
        print_error "Docker is not running. Please start Docker and try again."
        exit 1
    fi
}

# Function to build the Docker image
build() {
    print_status "Building Docker image..."
    docker-compose build
    print_success "Docker image built successfully!"
}

# Function to start the development environment
dev() {
    print_status "Starting development environment..."
    docker-compose up -d
    print_success "Development environment started!"
    print_status "Entering container shell..."
    docker-compose exec yiqqi-ai-workflows /bin/bash
}

# Function to run a specific Python script
run() {
    if [ -z "$1" ]; then
        print_error "Please specify a Python script to run"
        echo "Usage: $0 run <script.py>"
        exit 1
    fi
    
    print_status "Running $1 in container..."
    docker-compose run --rm yiqqi-ai-workflows python "$1"
}

# Function to install new dependencies
install() {
    if [ -z "$1" ]; then
        print_error "Please specify a package to install"
        echo "Usage: $0 install <package_name>"
        exit 1
    fi
    
    print_status "Installing $1..."
    docker-compose exec yiqqi-ai-workflows uv pip install "$1"
    print_warning "Don't forget to update requirements.txt if you want to persist this dependency!"
}

# Function to update requirements.txt
freeze() {
    print_status "Updating requirements.txt..."
    docker-compose exec yiqqi-ai-workflows uv pip freeze > requirements.txt
    print_success "requirements.txt updated!"
}

# Function to stop and clean up
clean() {
    print_status "Stopping and cleaning up containers..."
    docker-compose down
    print_status "Removing unused Docker resources..."
    docker system prune -f
    print_success "Cleanup completed!"
}

# Function to show logs
logs() {
    docker-compose logs -f yiqqi-ai-workflows
}

# Function to run tests
test() {
    print_status "Running tests in container..."
    docker-compose run --rm yiqqi-ai-workflows python -m pytest
}

# Function to start Jupyter notebook
jupyter() {
    print_status "Starting Jupyter notebook server..."
    docker-compose run --rm -p 8888:8888 yiqqi-ai-workflows \
        jupyter notebook --ip=0.0.0.0 --port=8888 --no-browser --allow-root
}

# Function to show help
help() {
    echo "YIQQI AI Workflows Docker Development Helper"
    echo ""
    echo "Usage: $0 <command> [arguments]"
    echo ""
    echo "Commands:"
    echo "  build           Build the Docker image"
    echo "  dev             Start development environment and enter shell"
    echo "  run <script>    Run a specific Python script"
    echo "  install <pkg>   Install a new Python package"
    echo "  freeze          Update requirements.txt with current packages"
    echo "  clean           Stop containers and clean up Docker resources"
    echo "  logs            Show container logs"
    echo "  test            Run tests"
    echo "  jupyter         Start Jupyter notebook server"
    echo "  help            Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 build"
    echo "  $0 dev"
    echo "  $0 run VideoAnalysisAI.py"
    echo "  $0 install numpy"
    echo "  $0 jupyter"
}

# Main script logic
check_docker

case "$1" in
    build)
        build
        ;;
    dev)
        dev
        ;;
    run)
        run "$2"
        ;;
    install)
        install "$2"
        ;;
    freeze)
        freeze
        ;;
    clean)
        clean
        ;;
    logs)
        logs
        ;;
    test)
        test
        ;;
    jupyter)
        jupyter
        ;;
    help|--help|-h)
        help
        ;;
    *)
        print_error "Unknown command: $1"
        echo ""
        help
        exit 1
        ;;
esac
